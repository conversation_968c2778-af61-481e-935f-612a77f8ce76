#!/usr/bin/env python3
"""
TomoPrint Web 应用程序
====================

基于Flask的TomoPrint云服务Web应用程序。
集成用户管理、角色管理、菜单管理和系统设置功能。
使用SQLite3数据库存储数据。
"""

import os
import uuid
import json
import time
from datetime import datetime
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_file, send_from_directory, redirect, url_for, session, flash
from werkzeug.utils import secure_filename
from functools import wraps
import threading
import logging

# 导入数据库模型
from models import (
    db, user_model, role_model, menu_model, settings_model, job_model
)

# 导入邮件服务
from email_service import send_job_notification

# 导入管理员蓝图
from admin import admin_bp

# 导入TomoPrint处理器
try:
    from tomo_processor import TomoProcessor, simulate_processing
    SIMULATION_MODE = False
except ImportError as e:
    print(f"warning: TomoEngine is not available: {e}")
    print("Run in simulation mode...")
    from tomo_processor import simulate_processing
    SIMULATION_MODE = True

# 配置
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'

app = Flask(__name__)
app.config['SECRET_KEY'] = 'tomoprint-secret-key-2024-secure'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['OUTPUT_FOLDER'] = OUTPUT_FOLDER

# 注册管理员蓝图
app.register_blueprint(admin_bp)

# 创建目录
Path(UPLOAD_FOLDER).mkdir(exist_ok=True)
Path(OUTPUT_FOLDER).mkdir(exist_ok=True)
Path('jobs').mkdir(exist_ok=True)

# 从数据库获取配置
def get_app_config():
    """从数据库获取应用配置"""
    try:
        max_file_size = settings_model.get_setting('system', 'max_file_size')
        allowed_extensions = settings_model.get_setting('system', 'allowed_extensions')

        if max_file_size:
            app.config['MAX_CONTENT_LENGTH'] = int(max_file_size['value']) * 1024 * 1024

        if allowed_extensions:
            global ALLOWED_EXTENSIONS
            ALLOWED_EXTENSIONS = set(allowed_extensions['value'].split(','))
    except:
        # 使用默认配置
        app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024
        ALLOWED_EXTENSIONS = {'stl', 'obj'}

# 初始化配置
get_app_config()

# 记录应用启动时间
app_start_time = datetime.now()

def login_required(f):
    """装饰器：要求用户登录"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def menu_permission_required(menu_url, permission_type='view'):
    """装饰器：基于菜单权限检查访问权限"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if 'user_id' not in session:
                return redirect(url_for('login'))

            user = get_current_user()
            if not user:
                flash('User not found', 'error')
                return redirect(url_for('login'))

            # 检查用户是否有访问该菜单的权限
            if not check_menu_permission(user['id'], menu_url, permission_type):
                flash('You do not have permission to access this page', 'error')
                return redirect(url_for('index'))

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def advanced_required(f):
    """装饰器：要求Advanced或Admin权限（保持向后兼容）"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))

        user = get_current_user()
        if not user:
            flash('User not found', 'error')
            return redirect(url_for('login'))

        # 检查用户是否有访问Jobs菜单的权限
        if not check_menu_permission(user['id'], '/jobs', 'view'):
            flash('You need Advanced privileges to access this page', 'error')
            return redirect(url_for('index'))

        return f(*args, **kwargs)
    return decorated_function

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def generate_job_id():
    """生成唯一任务ID"""
    return str(uuid.uuid4())

def get_current_user():
    """获取当前登录用户"""
    if 'user_id' in session:
        return user_model.get_user_by_id(session['user_id'])
    return None

def get_user_menus():
    """获取当前用户的菜单"""
    user = get_current_user()
    if user:
        return menu_model.get_user_menus(user['id'])
    return []

def check_menu_permission(user_id, menu_url, permission_type='view'):
    """检查用户是否有访问指定菜单的权限"""
    try:
        # 获取用户的有效权限
        permissions = user_model.get_user_effective_permissions(user_id)

        # 查找匹配的菜单权限
        for perm in permissions:
            if perm['url'] == menu_url:
                permission_field = f"can_{permission_type}"
                return bool(perm.get(permission_field, False))

        # 如果没有找到匹配的菜单，检查是否是管理员
        user = user_model.get_user_by_id(user_id)
        if user and user.get('role_id') == 1:  # Admin角色通常有所有权限
            return True

        return False
    except Exception as e:
        print(f"权限检查失败: {e}")
        return False

def has_menu_permission(menu_url, permission_type='view'):
    """检查当前用户是否有访问指定菜单的权限（用于模板）"""
    user = get_current_user()
    if not user:
        return False
    return check_menu_permission(user['id'], menu_url, permission_type)

# 模板全局变量
@app.context_processor
def inject_globals():
    """注入模板全局变量"""
    import sys
    import platform

    return {
        'current_user': get_current_user(),
        'user_menus': get_user_menus(),
        'has_menu_permission': has_menu_permission,
        'site_name': settings_model.get_setting('system', 'site_name')['value'] if settings_model.get_setting('system', 'site_name') else 'TomoPrint',
        'python_version': lambda: f"{platform.python_implementation()} {sys.version.split()[0]}",
        'platform_info': lambda: platform.platform(),
        'app_start_time': app_start_time.isoformat()
    }

def send_confirmation_email(email, job_id, parameters, geometry_info):
    """发送确认邮件给用户"""
    try:
        # 获取邮件设置
        smtp_settings = {}
        email_settings = settings_model.get_settings_by_category('email')
        for setting in email_settings:
            smtp_settings[setting['key']] = setting['value']

        # 邮件内容
        email_content = f"""
=== TomoPrint Job Confirmation ===
To: {email}
Subject: TomoPrint Job Confirmation - {job_id}

Dear User,

Thank you for using TomoPrint! Your job has been submitted successfully.

Job ID: {job_id}
Submission Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

PARAMETERS SUMMARY:
- Reorientation: {'Enabled' if parameters['reorientate'] else 'Disabled'}
- Resolution: {parameters['resolution']}%
- Number of Projections: {parameters['projections']}
- Brightness (Gamma): {parameters['brightness']}

GEOMETRY ANALYTICS:
- File Name: {geometry_info['filename']}
- File Size: {geometry_info['file_size']} MB
- Estimated Processing Time: {geometry_info['estimated_time']} minutes

STATUS PAGE:
You can check your job status at: http://localhost:5000/status/{job_id}

Your video will be available for download once processing is complete.

Best regards,
TomoPrint Team
================================
        """

        # 目前打印到控制台，可以扩展为真实的邮件发送
        print(email_content)
        print(f"✅ Email notification sent to {email} for job {job_id}")

        # TODO: 实现真实的邮件发送
        # if smtp_settings.get('smtp_host'):
        #     # 使用SMTP发送邮件
        #     pass

        return True

    except Exception as e:
        print(f"❌ Error sending email: {e}")
        return False

def process_stl_file(job_id):
    """Process STL file and generate video (background task)"""
    try:
        # 从数据库获取任务信息
        job_data = job_model.get_job_by_id(job_id)
        if not job_data:
            print(f"Job {job_id} not found in database")
            return

        # 更新状态为处理中
        job_model.update_job_status(job_id, 'processing', 0, 'Starting processing...')

        def progress_callback(progress, message):
            """Update job progress"""
            job_model.update_job_status(job_id, 'processing', progress, message)

        # Get job parameters
        file_path = job_data['file_path']
        parameters = job_data['parameters']

        # Process the file
        if SIMULATION_MODE:
            print(f"Processing job {job_id} in simulation mode...")
            output_file = simulate_processing(job_id, file_path, parameters, progress_callback)
        else:
            print(f"Processing job {job_id} with TomoEngine...")
            processor = TomoProcessor(job_id, file_path, parameters, progress_callback)
            output_file = processor.process()

        if output_file and os.path.exists(output_file):
            # Calculate file info
            file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB

            # Mark as completed
            job_model.update_job_status(
                job_id, 'completed', 100, 'Processing completed',
                os.path.basename(output_file), output_file, round(file_size, 2)
            )

            print(f"Job {job_id} completed successfully - Output: {output_file}")

            # Send completion email notification
            try:
                from email_service import send_job_notification
                from models import settings_model

                # Get user email from job data
                user_email = job_data.get('email')
                if user_email:
                    # Get base URL from system settings
                    base_url_setting = settings_model.get_setting('system', 'base_url')
                    base_url = base_url_setting['value'] if base_url_setting else 'http://78.47.15.208:5000'
                    download_url = f"{base_url}/download/{job_id}"
                    original_filename = job_data.get('original_filename', 'Unknown')

                    success = send_job_notification(
                        user_email,
                        job_id,
                        original_filename,
                        'completed',
                        download_url=download_url
                    )

                    if success:
                        print(f"✅ Completion email sent to {user_email} for job {job_id}")
                    else:
                        print(f"❌ Failed to send completion email to {user_email} for job {job_id}")
                else:
                    print(f"⚠️  No email address found for job {job_id}")

            except Exception as email_error:
                print(f"❌ Error sending completion email for job {job_id}: {email_error}")

        else:
            raise Exception("Processing completed but no output file was generated")

    except Exception as e:
        # Mark as failed
        job_model.update_job_status(
            job_id, 'failed', 0, 'Failed',
            error_message=str(e)
        )
        print(f"Job {job_id} failed: {e}")
        logging.error(f"Job {job_id} processing error: {e}", exc_info=True)

        # Send failure email notification
        try:
            from email_service import send_job_notification

            # Get user email from job data
            user_email = job_data.get('email')
            if user_email:
                original_filename = job_data.get('original_filename', 'Unknown')

                success = send_job_notification(
                    user_email,
                    job_id,
                    original_filename,
                    'failed',
                    error_message=str(e)
                )

                if success:
                    print(f"✅ Failure email sent to {user_email} for job {job_id}")
                else:
                    print(f"❌ Failed to send failure email to {user_email} for job {job_id}")
            else:
                print(f"⚠️  No email address found for job {job_id}")

        except Exception as email_error:
            print(f"❌ Error sending failure email for job {job_id}: {email_error}")

@app.route('/login', methods=['GET', 'POST'])
def login():
    """登录页面"""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()

        if not username or not password:
            flash('Username and password cannot be empty', 'error')
            return render_template('login.html')

        # 用户认证
        user = user_model.authenticate(username, password)
        if user:
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['role_id'] = user['role_id']
            session['role_name'] = user['role_name']

            flash(f'Welcome back, {user["full_name"] or user["username"]}!', 'success')

            # 所有用户登录后都重定向到首页
            return redirect(url_for('index'))
        else:
            flash('Invalid username or password', 'error')

    return render_template('login.html')

@app.route('/logout')
def logout():
    """登出"""
    session.clear()
    flash('Log out successfully', 'info')
    return redirect(url_for('index'))

@app.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '').strip()
        confirm_password = request.form.get('confirm_password', '').strip()
        full_name = request.form.get('full_name', '').strip()

        # 验证输入
        if not all([username, email, password, confirm_password]):
            flash('All fields are required', 'error')
            return render_template('register.html')

        if password != confirm_password:
            flash('The passwords entered twice are inconsistent', 'error')
            return render_template('register.html')

        if len(password) < 6:
            flash('Password length is at least 6 digits', 'error')
            return render_template('register.html')

        # 创建用户
        user_id = user_model.create_user(username, email, password, full_name, role_id=3)

        if user_id:
            flash('Registration is successful, please log in', 'success')
            return redirect(url_for('login'))
        else:
            flash('Registration failed, username or email may already exist', 'error')

    return render_template('register.html')

@app.route('/')
def index():
    """Main page - accessible to all users"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and job submission - accessible to all users"""
    try:
        # Check if file is present
        if 'file' not in request.files:
            return jsonify({'error': 'No file selected'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Check file type
        if not allowed_file(file.filename):
            return jsonify({'error': 'Invalid file type. Please upload STL or OBJ files.'}), 400

        # Get current user (may be None for guest users)
        current_user = get_current_user()

        # Get form data
        email = request.form.get('email', '').strip()
        if not email:
            # 如果没有提供邮箱，尝试使用当前用户的邮箱
            email = current_user['email'] if current_user else ''

        if not email:
            return jsonify({'error': 'Email address is required'}), 400

        # Get advanced parameters
        reorientate = request.form.get('reorientate') == 'on'
        resolution = int(request.form.get('resolution', 50))
        projections = int(request.form.get('projections', 360))
        brightness = float(request.form.get('brightness', 0.8))

        # Get model position and rotation parameters
        model_pos_x = float(request.form.get('model_pos_x', 0.0))
        model_pos_y = float(request.form.get('model_pos_y', 0.0))
        model_pos_z = float(request.form.get('model_pos_z', 0.0))
        model_rot_x = float(request.form.get('model_rot_x', 0.0))
        model_rot_y = float(request.form.get('model_rot_y', 0.0))
        model_rot_z = float(request.form.get('model_rot_z', 0.0))

        # Get model scale parameter
        model_scale = float(request.form.get('model_scale', 100.0))

        # Get container parameters
        container_outer_diameter = request.form.get('container_outer_diameter')
        container_inner_diameter = request.form.get('container_inner_diameter')

        # Convert to float if provided, otherwise None (use defaults)
        if container_outer_diameter:
            container_outer_diameter = float(container_outer_diameter)
        else:
            container_outer_diameter = None

        if container_inner_diameter:
            container_inner_diameter = float(container_inner_diameter)
        else:
            container_inner_diameter = None

        # Generate job ID and save file
        job_id = generate_job_id()
        filename = secure_filename(file.filename)
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], f"{job_id}_{filename}")
        file.save(file_path)

        # Get file info
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
        estimated_time = max(5, int(file_size * 2))  # Rough estimate

        # Create job record
        job_data = {
            'id': job_id,
            'user_id': current_user['id'] if current_user else None,
            'email': email,
            'filename': filename,
            'file_path': file_path,
            'file_size': round(file_size, 2),
            'parameters': {
                'reorientate': reorientate,
                'resolution': resolution,
                'projections': projections,
                'brightness': brightness,
                'model_position': {
                    'x': model_pos_x,
                    'y': model_pos_y,
                    'z': model_pos_z
                },
                'model_rotation': {
                    'x': model_rot_x,
                    'y': model_rot_y,
                    'z': model_rot_z
                },
                'model_scale': model_scale,
                'container_outer_diameter': container_outer_diameter,
                'container_inner_diameter': container_inner_diameter
            },
            'status': 'submitted',
            'estimated_time': estimated_time,
            'progress': 0,
            'current_step': 'Queued'
        }

        # Save to database
        job_model.create_job(job_data)

        # Send confirmation email
        geometry_info = {
            'filename': filename,
            'file_size': round(file_size, 2),
            'estimated_time': estimated_time
        }

        send_confirmation_email(email, job_id, job_data['parameters'], geometry_info)

        # Start background processing
        processing_thread = threading.Thread(target=process_stl_file, args=(job_id,))
        processing_thread.daemon = True
        processing_thread.start()

        return jsonify({
            'success': True,
            'job_id': job_id,
            'message': 'File uploaded successfully! Check your email for confirmation.',
            'status_url': f'/status/{job_id}'
        })

    except Exception as e:
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500

@app.route('/status/<job_id>')
def job_status(job_id):
    """Job status page - accessible to all users"""
    job = job_model.get_job_by_id(job_id)
    if not job:
        return render_template('error.html',
                             error="Job not found",
                             message="The requested job ID does not exist.")

    return render_template('status.html', job=job)

@app.route('/api/status/<job_id>')
def api_job_status(job_id):
    """API endpoint for job status - accessible to all users"""
    job = job_model.get_job_by_id(job_id)
    if not job:
        return jsonify({'error': 'Job not found'}), 404

    response_data = {
        'id': job['id'],
        'status': job['status'],
        'progress': job['progress'],
        'current_step': job['current_step'],
        'estimated_time': job.get('estimated_time', 0),
        'submitted_at': job['submitted_at'],
        'completed_at': job.get('completed_at'),
        'output_file': job.get('output_file'),
        'output_size': job.get('output_size'),
        'error_message': job.get('error_message'),
        'filename': job.get('filename'),
        'parameters': job.get('parameters', {})
    }

    # Add download URL if completed
    if job['status'] == 'completed' and job.get('output_file'):
        response_data['download_url'] = f'/download/{job_id}'

    return jsonify(response_data)

@app.route('/download/<job_id>')
def download_file(job_id):
    """Download generated video - accessible to all users"""

    # Get job from database
    job = job_model.get_job_by_id(job_id)
    if not job:
        return "Job not found", 404

    if job['status'] != 'completed':
        return "Job not completed yet", 400

    # Try to get output path from job data
    output_path = job.get('output_path')

    # If no output path in database, try to find the file
    if not output_path or not os.path.exists(output_path):
        possible_paths = [
            f"outputs/{job_id}/{job_id}_David_head_rotation.mp4",
            f"outputs\\{job_id}\\{job_id}_David_head_rotation.mp4",
            f"outputs/{job_id}/{job_id}_rotation.mp4",
            f"outputs\\{job_id}\\{job_id}_rotation.mp4"
        ]

        for path in possible_paths:
            abs_path = os.path.abspath(path)
            if os.path.exists(abs_path):
                output_path = abs_path
                break
            elif os.path.exists(path):
                output_path = path
                break

    if output_path and os.path.exists(output_path):
        try:
            filename = job.get('filename', 'model')
            base_name = os.path.splitext(filename)[0]
            download_name = f"tomoprint_{base_name}_rotation.mp4"

            print(f"Sending file: {output_path}")
            return send_file(
                output_path,
                as_attachment=True,
                download_name=download_name,
                mimetype='video/mp4'
            )
        except Exception as e:
            print(f"Error sending file {output_path}: {e}")
            return "File download failed", 500
    else:
        print(f"File not found: {output_path}")
        return "Generated file not found", 404

@app.route('/jobs')
@advanced_required
def list_jobs():
    """List all jobs"""
    current_user = get_current_user()

    # Admin可以看到所有任务，Advanced用户只能看到自己的任务
    if current_user and current_user['role_id'] == 1:  # Admin
        jobs = job_model.get_all_jobs()
    else:  # Advanced用户
        # 获取当前用户的任务
        from models import db
        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM jobs WHERE user_id = ? ORDER BY submitted_at DESC', (current_user['id'],))
        jobs = [dict(row) for row in cursor.fetchall()]

        # 解析参数JSON
        for job in jobs:
            if job['parameters']:
                import json
                job['parameters'] = json.loads(job['parameters'])

        conn.close()

    return render_template('jobs.html', jobs=jobs)

@app.route('/test')
@advanced_required
def test_page():
    """Test page for development and debugging"""
    return render_template('test.html')

@app.route('/model-viewer')
@advanced_required
def model_viewer():
    """3D Model Viewer page for STL visualization and container positioning"""
    return render_template('model_viewer.html')

@app.route('/api/upload-stl', methods=['POST'])
@advanced_required
def upload_stl_for_viewer():
    """Upload STL file for 3D viewer"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': 'No file uploaded'})

    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'error': 'No file selected'})

    # Check file extension
    if not file.filename.lower().endswith(('.stl', '.obj')):
        return jsonify({'success': False, 'error': 'Only STL and OBJ files are supported'})

    try:
        # Save file to uploads directory
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_filename = f"{timestamp}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(file_path)

        return jsonify({
            'success': True,
            'filename': unique_filename,
            'file_path': f'/uploads/{unique_filename}'
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Serve uploaded files"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/api/system/status')
def system_status():
    """API endpoint for system status"""
    return jsonify({
        'mode': 'simulation' if SIMULATION_MODE else 'production',
        'tomoengine_available': not SIMULATION_MODE,
        'version': '2.0.0',
        'status': 'operational',
        'database': 'sqlite3',
        'features': [
            'User Management',
            'Role Management',
            'Menu Management',
            'System Settings',
            'Email Configuration'
        ]
    })

if __name__ == '__main__':
    print("Starting TomoPrint Web Application v2.0...")
    print("Access the application at: http://localhost:5000")
    print("\nNew Features:")
    print("- User Management System")
    print("- Role-based Access Control")
    print("- Menu Management")
    print("- System Settings")
    print("- SQLite3 Database")
    print("- Email Configuration")
    print("\nDefault Admin Account:")
    print("Username: admin")
    print("Password: admin123")
    app.run(debug=True, host='0.0.0.0', port=5000)
