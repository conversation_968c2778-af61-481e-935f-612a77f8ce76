#!/usr/bin/env python3
"""
TomoPrint Web Processor
======================

Web processing module for TomoPrint that delegates to the core tomo_print_core.py
for consistent, high-quality results. This module provides web-specific features
like progress tracking and job management while using the proven core processing logic.
"""

import os
import sys
import time
import gc
import psutil
from datetime import datetime
import threading
from pathlib import Path

# 配置环境变量以确保后台静默运行
os.environ['MPLBACKEND'] = 'Agg'  # 使用非交互式matplotlib后端
os.environ['DISPLAY'] = ''  # 禁用X11显示（Linux环境）
os.environ['QT_QPA_PLATFORM'] = 'offscreen'  # Qt离屏渲染

# 智能选择OpenGL平台（修复CUDA安装后的兼容性问题）
if sys.platform.startswith('win'):
    # Windows系统使用原生OpenGL支持
    os.environ['PYOPENGL_PLATFORM'] = 'win32'
    print("🪟 使用Windows原生OpenGL平台")
else:
    # Linux系统使用GLX或OSMesa
    os.environ['PYOPENGL_PLATFORM'] = 'glx'
    print("🐧 使用Linux GLX OpenGL平台")

# 在导入matplotlib之前设置后端
import matplotlib
matplotlib.use('Agg')  # 确保使用非交互式后端
import matplotlib.pyplot as plt
plt.ioff()  # 关闭交互模式

# Add TomoEngine to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'TomoEngine'))

# Import the core processing function
try:
    from tomo_print_core import run_tomo_print
    CORE_AVAILABLE = True
    print("✅ Successfully imported tomo_print_core.run_tomo_print")
except ImportError as e:
    print(f"⚠️  Warning: Could not import tomo_print_core: {e}")
    print("Running in simulation mode...")
    CORE_AVAILABLE = False

class TomoProcessor:
    """
    Web processor class that delegates to tomo_print_core.run_tomo_print

    This class provides web-specific features like progress tracking and job management
    while ensuring consistent results by using the core processing logic.
    """

    def __init__(self, job_id, file_path, parameters, progress_callback=None):
        self.job_id = job_id
        self.file_path = file_path
        self.parameters = parameters
        self.progress_callback = progress_callback

        # Processing state
        self.is_processing = False
        self.processing_thread = None
        self.result_video_path = None
        self.error_message = None

        # Setup paths
        self.obj_name = os.path.splitext(os.path.basename(file_path))[0]

        # Use absolute path for output directory
        current_dir = Path(__file__).parent
        self.output_dir = current_dir / 'outputs' / job_id
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # Convert web parameters to core parameters
        self.core_params = self._convert_web_params_to_core_params()

        # Extract model position and rotation parameters
        self.model_position = parameters.get('model_position', {'x': 0.0, 'y': 0.0, 'z': 0.0})
        self.model_rotation = parameters.get('model_rotation', {'x': 0.0, 'y': 0.0, 'z': 0.0})

        # Extract container parameters
        self.container_outer_diameter = parameters.get('container_outer_diameter')
        self.container_inner_diameter = parameters.get('container_inner_diameter')

    def _convert_web_params_to_core_params(self):
        """Convert web application parameters to core processing parameters"""

        # Default core parameters for web application (基于优化后的tomo_print_core.py)
        # 高分辨率设置：使用原始参数，GPU显存不足时直接报错
        core_params = {
            'optimize': False,     # 默认关闭优化以提高处理速度
            'voxelize': True,      # 启用体素化，使用高分辨率参数
            'visualize': False,    # Always false in web environment
            'display': True,
            'naive_renderer': True,  # 默认使用简单渲染器，确保快速稳定处理
            'iterations': 15,      # 使用优化后的默认值
            'fps': 15,
            'render_frames': 360,  # 降低默认帧数以提高处理速度
            'voxel_factor': 2,     # 恢复原始体素因子，保持高分辨率
            'scale_factor': 1.0,   # 恢复原始缩放
            'use_high_quality_video': False,  # 使用标准质量以提高兼容性和减少内存使用
            'camera_distance': None,  # 自动计算相机距离
            'auto_adjust_camera': True,  # 启用自动相机调整
            'auto_scale_model': True,  # 启用自动模型缩放
            'target_model_size': None,  # 自动计算目标尺寸
            'aggressive_scaling': True,  # 启用激进缩放策略
            'disable_memory_optimization': True  # 禁用内存优化，显存不足时直接报错
        }

        # Apply user-specified parameters
        if self.parameters.get('resolution'):
            # Scale render frames based on resolution percentage
            base_frames = 180
            resolution_factor = self.parameters['resolution'] / 100.0
            core_params['render_frames'] = max(60, int(base_frames * resolution_factor))

        if self.parameters.get('projections'):
            core_params['render_frames'] = self.parameters['projections']

        if self.parameters.get('iterations'):
            # Allow user to configure optimization iterations
            core_params['iterations'] = max(1, min(20, self.parameters['iterations']))

        # Map other web-specific parameters (基于优化后的参数)
        if self.parameters.get('quality') == 'fast':
            core_params.update({
                'optimize': False,
                'naive_renderer': True,
                'render_frames': 180,  # 快速模式使用较少帧数
                'voxel_factor': 4,     # 快速模式使用较低精度
                'iterations': 5        # 快速模式使用较少迭代
            })
        elif self.parameters.get('quality') == 'high':
            core_params.update({
                'optimize': True,      # 高质量模式启用优化
                'naive_renderer': False,  # 高质量模式不使用简单渲染器
                'iterations': 20,      # 高质量模式使用更多迭代
                'render_frames': 720,  # 高质量模式使用更多帧数（从1080降低以平衡性能）
                'voxel_factor': 1      # 高质量模式使用最高精度
            })

        return core_params

    def update_progress(self, progress, message):
        """Update processing progress with memory monitoring"""
        if self.progress_callback:
            self.progress_callback(progress, message)

        # Monitor memory usage
        memory_info = psutil.virtual_memory()
        memory_percent = memory_info.percent
        memory_available_gb = memory_info.available / (1024**3)

        print(f"[{self.job_id}] {progress}% - {message} (RAM: {memory_percent:.1f}%, Available: {memory_available_gb:.1f}GB)")

        # Warning if memory usage is high
        if memory_percent > 85:
            print(f"⚠️  WARNING: High memory usage ({memory_percent:.1f}%)")
            gc.collect()  # Force garbage collection

    def _run_core_processing(self):
        """Run the core processing using tomo_print_core.run_tomo_print with retry mechanism"""
        if not CORE_AVAILABLE:
            raise ImportError("Core processing module not available")

        self.update_progress(5, "Starting core processing...")

        # Generate output video path
        output_video_path = self.output_dir / f"{self.obj_name}_rotation.mp4"

        # 多次尝试处理，逐步调整参数以避免GPU内存不足
        max_attempts = 3
        current_params = self.core_params.copy()

        for attempt in range(max_attempts):
            try:
                print(f"🔄 处理尝试 {attempt + 1}/{max_attempts}")
                print(f"   参数: voxel_factor={current_params.get('voxel_factor', 'N/A')}, "
                      f"render_frames={current_params.get('render_frames', 'N/A')}")

                self.update_progress(10 + attempt * 5, f"Processing attempt {attempt + 1}...")

                # Call the core processing function
                result = run_tomo_print(
                    input_file_path=self.file_path,
                    output_video_path=str(output_video_path),
                    output_dir=str(self.output_dir),
                    model_position=self.model_position,
                    model_rotation=self.model_rotation,
                    container_outer_diameter=self.container_outer_diameter,
                    container_inner_diameter=self.container_inner_diameter,
                    **current_params
                )

                if result and os.path.exists(result):
                    self.update_progress(100, "Processing completed successfully")
                    return result
                else:
                    raise Exception("Processing completed but no output file was generated")

            except Exception as e:
                error_msg = str(e)
                print(f"❌ 尝试 {attempt + 1} 失败: {error_msg}")

                # 检查是否是GPU显存不足错误
                is_gpu_memory_error = any(keyword in error_msg for keyword in
                                        ['GPU显存不足', 'GPU内存不足', 'RuntimeError'])

                # 检查是否是一般内存相关错误
                is_memory_error = any(keyword in error_msg.lower() for keyword in
                                    ['memory', '内存', 'out of memory', 'gl_out_of_memory', '1285'])

                if is_gpu_memory_error:
                    # GPU显存不足，直接报错，不重试
                    gpu_error_msg = f"GPU显存不足：当前模型需要的显存超过了GPU容量。{error_msg}"
                    print(f"💥 GPU显存不足错误: {gpu_error_msg}")
                    self.update_progress(0, f"GPU memory insufficient: {gpu_error_msg}")
                    self.error_message = gpu_error_msg

                    # 发送GPU显存不足邮件通知
                    self._send_gpu_memory_error_email(gpu_error_msg)
                    return None

                if attempt < max_attempts - 1:
                    if is_memory_error:
                        # 调整参数以减少内存使用
                        current_params['voxel_factor'] = current_params.get('voxel_factor', 4) * 1.5
                        current_params['render_frames'] = max(180, int(current_params.get('render_frames', 360) * 0.8))
                        current_params['scale_factor'] = current_params.get('scale_factor', 0.8) * 0.9

                        self.update_progress(15 + attempt * 5,
                                           f"Memory issue detected, adjusting parameters for retry {attempt + 2}...")
                        print(f"🔧 调整参数: voxel_factor={current_params['voxel_factor']:.1f}, "
                              f"render_frames={current_params['render_frames']}")
                    else:
                        # 非内存错误，稍作调整后重试
                        current_params['voxel_factor'] = current_params.get('voxel_factor', 4) * 1.2
                        self.update_progress(15 + attempt * 5, f"Adjusting parameters for retry {attempt + 2}...")

                    # 清理可能的残留文件
                    if os.path.exists(output_video_path):
                        try:
                            os.remove(output_video_path)
                        except:
                            pass

                    # 强制垃圾回收
                    gc.collect()

                else:
                    # 最后一次尝试也失败了
                    self.update_progress(0, f"All processing attempts failed: {error_msg}")
                    self.error_message = error_msg
                    return None

        # 不应该到达这里
        self.update_progress(0, "Processing failed: Unknown error")
        return None

    def _send_gpu_memory_error_email(self, error_msg):
        """发送GPU显存不足错误邮件通知"""
        try:
            from tomoprint_web.email_service import send_email
            from tomoprint_web.models import SystemSettings

            # 获取系统设置
            settings = SystemSettings.get_settings()
            if not settings.get('email_enabled', False):
                print("📧 邮件服务未启用，跳过GPU显存错误通知")
                return

            # 构建邮件内容
            subject = "🚨 TomoEngine GPU显存不足错误"

            html_content = f"""
            <html>
            <body>
                <h2 style="color: #d32f2f;">🚨 GPU显存不足错误</h2>

                <div style="background-color: #ffebee; padding: 15px; border-left: 4px solid #d32f2f; margin: 10px 0;">
                    <h3>错误详情:</h3>
                    <p><strong>错误信息:</strong> {error_msg}</p>
                    <p><strong>任务ID:</strong> {self.job_id}</p>
                    <p><strong>文件名:</strong> {self.file_path.name if self.file_path else 'Unknown'}</p>
                    <p><strong>时间:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>

                <div style="background-color: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 10px 0;">
                    <h3>🔧 解决建议:</h3>
                    <ul>
                        <li>使用更大显存的GPU（建议16GB以上）</li>
                        <li>减小模型文件大小</li>
                        <li>降低体素化分辨率参数</li>
                        <li>联系系统管理员升级硬件</li>
                    </ul>
                </div>

                <div style="background-color: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 10px 0;">
                    <h3>📊 系统信息:</h3>
                    <p><strong>当前GPU:</strong> 显存不足以处理此任务</p>
                    <p><strong>建议GPU:</strong> NVIDIA RTX 4090 (24GB) 或更高</p>
                    <p><strong>处理模式:</strong> 高分辨率模式（禁用内存优化）</p>
                </div>

                <hr style="margin: 20px 0;">
                <p style="color: #666; font-size: 12px;">
                    此邮件由TomoEngine系统自动发送<br>
                    如需技术支持，请联系系统管理员
                </p>
            </body>
            </html>
            """

            # 发送邮件给管理员
            admin_email = settings.get('admin_email', '<EMAIL>')
            send_email(
                to_email=admin_email,
                subject=subject,
                html_content=html_content
            )

            print(f"📧 GPU显存错误邮件已发送给管理员: {admin_email}")

        except Exception as email_error:
            print(f"📧 发送GPU显存错误邮件失败: {email_error}")
            # 邮件发送失败不应该影响主要流程

    def process_async(self):
        """Start processing in a background thread"""
        if self.is_processing:
            print(f"⚠️  Job {self.job_id} is already processing")
            return False

        self.is_processing = True
        self.processing_thread = threading.Thread(target=self._process_worker)
        self.processing_thread.daemon = True
        self.processing_thread.start()

        return True

    def _process_worker(self):
        """Worker method that runs in background thread"""
        try:
            self.result_video_path = self._run_core_processing()
        except Exception as e:
            self.error_message = str(e)
            self.result_video_path = None
        finally:
            self.is_processing = False

    def process(self):
        """
        Main processing method - delegates to core processing

        Returns:
        str: Path to output video file, or None if failed
        """
        try:
            self.update_progress(0, "Starting processing...")

            if not CORE_AVAILABLE:
                # Fall back to simulation mode
                return self._simulate_processing()

            # Run core processing
            result = self._run_core_processing()

            if result:
                self.result_video_path = result
                return result
            else:
                return None

        except Exception as e:
            self.update_progress(0, f"Processing failed: {str(e)}")
            self.error_message = str(e)
            return None

    def _simulate_processing(self):
        """Simulate processing when core module is not available"""
        self.update_progress(10, "Simulating processing (core module not available)...")

        # Simulate processing steps with delays
        steps = [
            (20, "Initializing components..."),
            (30, "Processing geometry file..."),
            (40, "Starting voxelization..."),
            (50, "Voxelization completed"),
            (60, "Setting up renderer..."),
            (70, "Running optimization..."),
            (80, "Generating images..."),
            (90, "Creating video..."),
        ]

        for progress, message in steps:
            self.update_progress(progress, message)
            time.sleep(0.5)  # Simulate processing time

        # Create a dummy video file
        video_path = self.output_dir / f"{self.obj_name}_rotation.mp4"

        try:
            import cv2
            import numpy as np

            # Create a simple test video
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            video_writer = cv2.VideoWriter(str(video_path), fourcc, 30, (640, 480))

            for i in range(90):  # 3 second video at 30fps
                frame = np.zeros((480, 640, 3), dtype=np.uint8)
                # Draw a rotating line
                angle = i * 4  # 4 degrees per frame
                center = (320, 240)
                length = 100
                end_x = int(center[0] + length * np.cos(np.radians(angle)))
                end_y = int(center[1] + length * np.sin(np.radians(angle)))
                cv2.line(frame, center, (end_x, end_y), (255, 255, 255), 3)
                cv2.putText(frame, f"SIMULATION", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
                cv2.putText(frame, f"Frame {i+1}", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                video_writer.write(frame)

            video_writer.release()

            self.update_progress(100, "Simulation completed")
            return str(video_path)

        except Exception as e:
            self.update_progress(0, f"Simulation failed: {str(e)}")
            return None

    def get_status(self):
        """Get current processing status"""
        return {
            'is_processing': self.is_processing,
            'result_video_path': self.result_video_path,
            'error_message': self.error_message,
            'job_id': self.job_id
        }

    def wait_for_completion(self, timeout=None):
        """Wait for processing to complete"""
        if self.processing_thread:
            self.processing_thread.join(timeout)
        return not self.is_processing

def simulate_processing(job_id, file_path, parameters, progress_callback=None):
    """
    Simulation mode for testing without TomoEngine

    This function is kept for backward compatibility with existing web applications
    that might call it directly. New code should use TomoProcessor class.
    """

    # Create a TomoProcessor instance and use its simulation
    processor = TomoProcessor(job_id, file_path, parameters, progress_callback)
    return processor._simulate_processing()
