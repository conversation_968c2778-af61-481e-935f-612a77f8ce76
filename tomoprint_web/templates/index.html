{% extends "base.html" %}

{% block title %}Home - {{ site_name }}{% endblock %}
{% block page_title %}File Upload & Processing{% endblock %}

{% block extra_css %}
<style>
    .upload-area {
        border: 2px dashed #667eea;
        border-radius: 10px;
        padding: 40px;
        text-align: center;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }

    .upload-area:hover, .upload-area.dragover {
        border-color: #764ba2;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
    }

    .upload-icon {
        font-size: 48px;
        margin-bottom: 15px;
    }

    .upload-text {
        font-size: 18px;
        font-weight: 600;
        color: #667eea;
        margin-bottom: 5px;
    }

    .upload-subtext {
        color: #7f8c8d;
        font-size: 14px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #2c3e50;
    }

    .form-input {
        width: 100%;
        padding: 12px;
        border: 2px solid #ecf0f1;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.3s ease;
    }

    .form-input:focus {
        outline: none;
        border-color: #667eea;
    }

    .checkbox-group {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .checkbox {
        margin-right: 10px;
        transform: scale(1.2);
    }

    .advanced-options {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        background: #f8f9fa;
        border-radius: 8px;
        padding: 0 20px;
        margin-bottom: 20px;
    }

    .advanced-options.show {
        max-height: 500px;
        padding: 20px;
    }

    /* Models Options Styles */
    .models-options {
        overflow: hidden;
        max-height: 0;
        transition: max-height 0.3s ease;
        background: #e8f5e8;
        border-radius: 8px;
        padding: 0 20px;
        margin-bottom: 20px;
        border: 2px solid #28a745;
    }

    .models-options.show {
        max-height: 800px;
        padding: 20px;
    }

    .model-selection-group {
        margin-bottom: 20px;
    }

    .radio-group {
        display: flex;
        gap: 20px;
        margin-top: 8px;
    }

    .radio-input {
        margin-right: 8px;
    }

    .radio-label {
        cursor: pointer;
        font-weight: 500;
        color: #2c3e50;
    }

    .model-sub-options {
        margin-left: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #28a745;
        margin-bottom: 15px;
    }

    .custom-parameters {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .custom-parameters h4 {
        grid-column: 1 / -1;
        margin-bottom: 15px;
        color: #2c3e50;
        border-bottom: 2px solid #28a745;
        padding-bottom: 8px;
    }

    /* Container Settings Styles for form */
    #containerSettings.container-settings {
        overflow: hidden;
        max-height: 0;
        transition: max-height 0.3s ease;
        background: #fff3e0;
        border-radius: 8px;
        padding: 0 20px;
        margin-bottom: 20px;
        border: 2px solid #ff9800;
    }

    #containerSettings.container-settings.show {
        max-height: 600px;
        padding: 20px;
    }

    .container-parameters {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        align-items: start;
    }

    .container-parameters h4 {
        grid-column: 1 / -1;
        margin-bottom: 15px;
        color: #2c3e50;
        border-bottom: 2px solid #ff9800;
        padding-bottom: 8px;
    }

    .container-preview {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        border-left: 4px solid #ff9800;
    }

    .container-preview h5 {
        color: #2c3e50;
        margin-bottom: 10px;
    }

    .preview-info p {
        margin: 5px 0;
        font-size: 0.9em;
    }

    .container-actions {
        grid-column: 1 / -1;
        display: flex;
        gap: 10px;
        margin-top: 15px;
        justify-content: center;
    }

    .option-row {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 20px;
        margin-bottom: 20px;
    }

    @media (max-width: 768px) {
        .option-row {
            grid-template-columns: 1fr;
        }
    }

    .btn {
        width: 100%;
        padding: 15px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }

    .btn:hover:not(:disabled) {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-2px);
    }

    .btn:disabled {
        background: #bdc3c7;
        cursor: not-allowed;
        transform: none;
    }

    .progress-bar {
        width: 100%;
        height: 20px;
        background: #ecf0f1;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 10px;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        width: 0%;
        transition: width 0.3s ease;
    }

    /* Main Layout Styles */
    .main-layout {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        max-width: 1400px;
        margin: 0 auto;
    }

    .left-panel .card {
        max-width: none;
        margin: 0;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .right-panel .card {
        max-width: none;
        margin: 0;
        padding: 30px;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        height: fit-content;
    }

    /* 3D Viewer Styles */
    .threejs-container {
        width: 100%;
        height: 350px;
        border: 2px solid #ecf0f1;
        border-radius: 10px;
        margin: 15px 0;
        position: relative;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .loading-3d {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: #7f8c8d;
    }

    .loading-3d i {
        font-size: 2em;
        margin-bottom: 10px;
    }

    /* Container Settings for 3D Viewer */
    #viewer-container-settings.container-settings {
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        max-height: none;
        overflow: visible;
    }

    .container-settings h4 {
        margin: 0 0 15px 0;
        color: #2c3e50;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .collapse-arrow {
        font-size: 12px;
        transition: transform 0.3s ease;
    }

    .container-settings.collapsed .collapse-arrow {
        transform: rotate(-90deg);
    }

    .settings-grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 15px;
        transition: all 0.3s ease;
    }

    .container-settings.collapsed .settings-grid {
        display: none;
    }

    /* System Status Styles */
    .system-status {
        background: linear-gradient(135deg, #3498db, #2980b9);
        border: 2px solid #2471a3;
        border-radius: 8px;
        margin-bottom: 15px;
        padding: 12px;
        color: white;
        box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
    }

    .system-status.simulation {
        background: linear-gradient(135deg, #f39c12, #e67e22);
        border: 2px solid #d68910;
        box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
    }

    .status-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .status-content i {
        font-size: 16px;
        color: white;
    }

    /* Collision Warning Styles */
    .collision-warning {
        background: linear-gradient(135deg, #f39c12, #e67e22);
        border: 2px solid #d68910;
        border-radius: 8px;
        margin-bottom: 15px;
        padding: 15px;
        color: white;
        box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
        animation: warningPulse 2s infinite;
    }

    .warning-content {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
    }

    .warning-content i {
        font-size: 20px;
        color: #fff;
        margin-right: 10px;
    }

    .warning-details {
        width: 100%;
        margin-top: 8px;
        font-size: 13px;
        opacity: 0.9;
        line-height: 1.4;
    }

    @keyframes warningPulse {
        0%, 100% {
            box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
        }
        50% {
            box-shadow: 0 4px 20px rgba(243, 156, 18, 0.6);
        }
    }

    .settings-grid .form-group {
        margin-bottom: 0;
    }

    /* Geometry Analysis Styles */
    .geometry-analysis {
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        max-height: none;
        overflow: visible;
    }

    .geometry-analysis h4 {
        margin: 0 0 15px 0;
        color: #2c3e50;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .geometry-analysis.collapsed .analysis-grid {
        display: none;
    }

    .analysis-grid {
        display: flex;
        flex-direction: column;
        gap: 15px;
        transition: all 0.3s ease;
    }

    .analysis-section {
        background: white;
        padding: 12px;
        border-radius: 6px;
        border: 1px solid #e9ecef;
    }

    .analysis-section h5 {
        margin: 0 0 10px 0;
        color: #495057;
        font-size: 14px;
        font-weight: 600;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 5px;
    }

    .dimension-grid, .cross-section-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .dimension-item, .cross-section-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;
    }

    .dimension-item label, .cross-section-item label {
        font-size: 12px;
        color: #6c757d;
        font-weight: 500;
    }

    .dimension-item span, .cross-section-item span {
        font-size: 12px;
        color: #2c3e50;
        font-weight: 600;
        font-family: 'Courier New', monospace;
    }

    /* Keyboard Controls Help Styles */
    .keyboard-controls-help {
        margin-top: 15px;
        padding: 12px;
        background: #e8f4fd;
        border-radius: 6px;
        border: 1px solid #bee5eb;
    }

    .keyboard-controls-help h5 {
        margin: 0 0 10px 0;
        color: #0c5460;
        font-size: 14px;
        font-weight: 600;
    }

    .controls-grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 12px;
    }

    .control-section {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .control-section strong {
        font-size: 12px;
        color: #0c5460;
        margin-bottom: 4px;
    }

    .control-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 11px;
        color: #495057;
    }

    .key-combo {
        background: #fff;
        border: 1px solid #ced4da;
        border-radius: 3px;
        padding: 2px 6px;
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: #495057;
        min-width: 60px;
        text-align: center;
    }

    .settings-grid label {
        font-size: 12px;
        margin-bottom: 5px;
        display: block;
        font-weight: 500;
        color: #495057;
    }

    .settings-grid input {
        padding: 8px 10px;
        font-size: 14px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        width: 100%;
        transition: border-color 0.15s ease-in-out;
    }

    .settings-grid input:focus {
        border-color: #667eea;
        outline: none;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
    }

    /* Model Controls */
    .model-controls {
        margin-top: 15px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .model-controls h4 {
        margin: 0 0 12px 0;
        color: #2c3e50;
        font-size: 16px;
        font-weight: 600;
    }

    .controls-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
        margin-bottom: 15px;
    }

    .control-group label {
        display: block;
        font-weight: 600;
        margin-bottom: 8px;
        color: #2c3e50;
        font-size: 14px;
    }

    .position-controls, .rotation-controls, .container-controls {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .axis-control {
        display: grid;
        grid-template-columns: 20px 1fr auto;
        gap: 8px;
        align-items: center;
    }

    .container-controls .axis-control {
        grid-template-columns: 120px 1fr 40px;
    }

    .container-controls .axis-control .unit {
        font-size: 12px;
        color: #6c757d;
        font-weight: 500;
    }

    .readonly-input {
        background-color: #f8f9fa !important;
        color: #6c757d !important;
        cursor: not-allowed;
    }

    .container-info {
        margin-top: 8px;
        padding: 8px;
        background-color: #e3f2fd;
        border-radius: 4px;
        border-left: 3px solid #2196f3;
    }

    .container-info small {
        color: #1976d2;
        font-size: 12px;
    }

    .wall-thickness-display {
        margin-top: 4px;
        padding: 2px 6px;
        background-color: #f8f9fa;
        border-radius: 3px;
        border-left: 2px solid #28a745;
    }

    .wall-thickness-display small {
        color: #495057;
        font-size: 11px;
        font-weight: 500;
    }

    .wall-thickness-value {
        color: #28a745;
        font-weight: 600;
    }

    /* Scale Controls */
    .scale-controls {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .scale-presets {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
    }

    .scale-btn {
        padding: 4px 8px;
        border: 1px solid #ddd;
        background: #f8f9fa;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        transition: all 0.2s;
    }

    .scale-btn:hover {
        background: #e9ecef;
        border-color: #adb5bd;
    }

    .scale-btn.active {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }

    .scale-info {
        margin-top: 4px;
    }

    .scale-info small {
        color: #6c757d;
        font-size: 11px;
    }

    .axis-control label {
        margin: 0;
        font-size: 13px;
        font-weight: 600;
        color: #495057;
    }

    .axis-control input {
        padding: 5px 8px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 13px;
        transition: border-color 0.15s ease-in-out;
    }

    .axis-control input:focus {
        border-color: #667eea;
        outline: none;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
    }

    .axis-buttons {
        display: flex;
        gap: 4px;
    }

    .btn-axis, .btn-rotate {
        padding: 4px 8px;
        border: 1px solid #ced4da;
        background: white;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        transition: all 0.2s ease;
        min-width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-axis:hover, .btn-rotate:hover {
        background: #e9ecef;
        border-color: #adb5bd;
        transform: translateY(-1px);
    }

    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
        margin-top: 15px;
    }

    .action-buttons .btn {
        flex: 1;
        margin-bottom: 0;
        padding: 8px 12px;
        font-size: 13px;
        min-height: 36px;
    }

    .btn-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    }

    .btn-secondary:hover {
        background: linear-gradient(135deg, #5a6268 0%, #3d4043 100%);
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .main-layout {
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .threejs-container {
            height: 300px;
        }

        .right-panel .card {
            margin-top: 20px;
        }
    }

    @media (max-width: 768px) {
        .settings-grid {
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .controls-grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .action-buttons {
            flex-direction: column;
            gap: 8px;
        }

        .axis-control {
            grid-template-columns: 25px 1fr auto;
            gap: 6px;
        }

        .threejs-container {
            height: 250px;
        }

        .container-settings, .model-controls {
            padding: 12px;
        }
    }

    @media (max-width: 480px) {
        .settings-grid {
            grid-template-columns: 1fr;
        }

        .axis-control {
            grid-template-columns: 1fr;
            gap: 4px;
            text-align: center;
        }

        .axis-control label {
            font-weight: bold;
        }

        .axis-buttons {
            justify-content: center;
            margin-top: 4px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Welcome Information -->
{% if not current_user %}
<div class="alert alert-info mb-4">
    <h5><i class="fas fa-info-circle"></i> Welcome to {{ site_name }}!</h5>
    <p class="mb-2">You can upload and process your 3D files without registration. For advanced features and job management, please <a href="{{ url_for('login') }}" class="alert-link">login</a> or <a href="{{ url_for('register') }}" class="alert-link">create an account</a>.</p>
    <small class="text-muted">
        <strong>Basic Features:</strong> File upload & processing, Email notifications<br>
        <strong>Advanced Features:</strong> Job history, 3D model viewer, Advanced processing options
    </small>
</div>
{% else %}
<div class="alert alert-success mb-4">
    <h5><i class="fas fa-user-check"></i> Welcome back, {{ current_user.full_name or current_user.username }}!</h5>
    <p class="mb-0">You have access to all features. Upload your files below or explore <a href="{{ url_for('model_viewer') }}" class="alert-link">3D Model Viewer</a> and <a href="{{ url_for('list_jobs') }}" class="alert-link">Job History</a>.</p>
</div>
{% endif %}

<!-- Main Content Layout -->
<div class="main-layout">
    <!-- Left Panel: Upload and Settings -->
    <div class="left-panel">
        <div class="card">
    <form id="uploadForm" enctype="multipart/form-data">
        <!-- File Upload Area -->
        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">☁️</div>
            <div class="upload-text">Drag & drop your CAD file here</div>
            <div class="upload-subtext">or click to browse (STL, OBJ files supported)</div>
            <input type="file" id="fileInput" name="file" accept=".stl,.obj" style="display: none;">
        </div>

        <!-- File Info -->
        <div id="fileInfo" style="display: none;">
            <div class="alert alert-success">
                <strong>File selected:</strong> <span id="fileName"></span> (<span id="fileSize"></span>)
            </div>
        </div>

        <!-- Email Input -->
        <div class="form-group">
            <label class="form-label" for="email">Email Address</label>
            <input type="email" id="email" name="email" class="form-input"
                   placeholder="<EMAIL>"
                   {% if current_user %}value="{{ current_user.email }}"{% endif %}
                   required>
        </div>

        <!-- Advanced Options Toggle - Based on role configuration -->
        {% if current_user and (current_user.show_advanced_options or (current_user.role_id and current_user.role_id in [1, 2])) %}
        <div class="checkbox-group">
            <input type="checkbox" id="showAdvanced" class="checkbox">
            <label for="showAdvanced" class="form-label">Advanced Options</label>
        </div>

        <!-- Advanced Options Panel -->
        <div id="advancedOptions" class="advanced-options">
        {% else %}
        <!-- Hidden advanced options for Basic users, but keep default values -->
        <div id="advancedOptions" class="advanced-options" style="display: none;">
        {% endif %}
            <!-- Reorientation -->
            <div class="checkbox-group">
                <input type="checkbox" id="reorientate" name="reorientate" class="checkbox" checked>
                <label for="reorientate" class="form-label">Reorientate</label>
            </div>

            <!-- Parameters Grid -->
            <div class="option-row">
                <div class="form-group">
                    <label class="form-label" for="resolution">Resolution (%)</label>
                    <input type="range" id="resolution" name="resolution" 
                           min="1" max="100" value="50" class="form-input">
                    <div style="text-align: center; margin-top: 5px;">
                        <span id="resolutionValue">50</span>%
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label" for="projections"># of Projections</label>
                    <input type="number" id="projections" name="projections"
                           class="form-input" value="360" min="60" max="3600" step="60">
                </div>

                <div class="form-group">
                    <label class="form-label" for="iterations">Optimization Iterations</label>
                    <input type="number" id="iterations" name="iterations"
                           class="form-input" value="3" min="1" max="100" step="1">
                    <small style="color: #7f8c8d; font-size: 12px;">
                        More iterations = better quality but longer processing time
                    </small>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label" for="brightness">Brightness (Gamma)</label>
                <input type="range" id="brightness" name="brightness"
                       min="0.1" max="2.0" value="0.8" step="0.1" class="form-input">
                <div style="text-align: center; margin-top: 5px;">
                    <span id="brightnessValue">0.8</span>
                </div>
            </div>
        </div>

        <!-- Models Option Toggle - Based on role configuration -->
        {% if current_user and (current_user.show_models_option or (current_user.role_id and current_user.role_id in [1, 2])) %}
        <div class="checkbox-group">
            <input type="checkbox" id="showModels" class="checkbox">
            <label for="showModels" class="form-label">Models Option</label>
        </div>

        <!-- Models Option Panel -->
        <div id="modelsOptions" class="models-options" style="display: none;">
            <!-- Primary Model Selection -->
            <div class="model-selection-group">
                <div class="form-group">
                    <label class="form-label">Model Type</label>
                    <div class="radio-group">
                        <input type="radio" id="capsuleModel" name="modelType" value="capsule" class="radio-input">
                        <label for="capsuleModel" class="radio-label">Capsule Model</label>

                        <input type="radio" id="customModel" name="modelType" value="custom" class="radio-input">
                        <label for="customModel" class="radio-label">Custom</label>
                    </div>
                </div>
            </div>

            <!-- Capsule Model Options -->
            <div id="capsuleOptions" class="model-sub-options" style="display: none;">
                <div class="form-group">
                    <label class="form-label">Capsule Selection</label>
                    <div class="radio-group">
                        <input type="radio" id="capsuleByModel" name="capsuleType" value="model" class="radio-input">
                        <label for="capsuleByModel" class="radio-label">Model</label>

                        <input type="radio" id="capsuleBySerial" name="capsuleType" value="serial" class="radio-input">
                        <label for="capsuleBySerial" class="radio-label">Serial#</label>
                    </div>
                </div>

                <!-- Model Dropdown -->
                <div id="modelDropdown" class="form-group" style="display: none;">
                    <label class="form-label" for="modelSelect">Select Model</label>
                    <select id="modelSelect" name="modelSelect" class="form-input">
                        <option value="">Choose a model...</option>
                        <option value="TVP/9527">TVP/9527</option>
                        <option value="CAL-Q7.24">CAL-Q7.24</option>
                    </select>
                </div>

                <!-- Serial Number Input -->
                <div id="serialInput" class="form-group" style="display: none;">
                    <label class="form-label" for="serialNumber">Serial Number</label>
                    <input type="text" id="serialNumber" name="serialNumber" class="form-input" placeholder="Enter serial number">
                </div>
            </div>

            <!-- Custom Model Options -->
            <div id="customOptions" class="model-sub-options" style="display: none;">
                <div class="custom-parameters">
                    <h4 style="margin-bottom: 15px; color: #2c3e50;">Custom Parameters</h4>

                    <div class="form-group">
                        <label class="form-label" for="customOD">OD (Outer Diameter)</label>
                        <input type="number" id="customOD" name="customOD" class="form-input" value="27.5" step="0.1">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="customID">ID (Inner Diameter)</label>
                        <input type="number" id="customID" name="customID" class="form-input" value="25.1" step="0.1">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="customHeight">Height</label>
                        <input type="number" id="customHeight" name="customHeight" class="form-input" value="32.4" step="0.1">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="customRI1">RI-I (Refractive Index I)</label>
                        <input type="number" id="customRI1" name="customRI1" class="form-input" value="1.49" step="0.01">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="customRI2">RI-II (Refractive Index II)</label>
                        <input type="number" id="customRI2" name="customRI2" class="form-input" value="1.52" step="0.01">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="customOffsetsAxial">Offsets-Axial</label>
                        <input type="number" id="customOffsetsAxial" name="customOffsetsAxial" class="form-input" value="3" step="1">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="customOffsetsRadial">Offsets-Radial</label>
                        <input type="number" id="customOffsetsRadial" name="customOffsetsRadial" class="form-input" value="0" step="1">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="customAttenuation">Attenuation</label>
                        <input type="number" id="customAttenuation" name="customAttenuation" class="form-input" value="0.8" step="0.1">
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Container Settings Toggle - Based on role configuration -->
<!--?        {% if current_user and (current_user.show_container_settings or (current_user.role_id and current_user.role_id in [1, 2])) %}-->
<!--?        <div class="checkbox-group">-->
<!--?            <input type="checkbox" id="showContainer" class="checkbox">-->
<!--?            <label for="showContainer" class="form-label">Container Settings</label>-->
<!--?        </div>-->

        <!-- Container Settings Panel -->
        <div id="containerSettings" class="container-settings" style="display: none;">
            <div class="container-parameters">
                <h4 style="margin-bottom: 15px; color: #2c3e50;">
                    <i class="fas fa-cylinder"></i> Cylindrical Container
                </h4>

                <div class="form-group">
                    <label class="form-label" for="containerRadius">Radius (mm)</label>
                    <input type="number" id="containerRadius" name="containerRadius" class="form-input" value="50.0" step="0.1" min="1">
                    <small class="form-text text-muted">Container radius in millimeters</small>
                </div>

                <div class="form-group">
                    <label class="form-label" for="containerHeight">Height (mm)</label>
                    <input type="number" id="containerHeight" name="containerHeight" class="form-input" value="100.0" step="0.1" min="1">
                    <small class="form-text text-muted">Container height in millimeters</small>
                </div>

                <div class="container-preview">
                    <h5>Container Preview</h5>
                    <div class="preview-info">
                        <p><strong>Volume:</strong> <span id="containerVolume">785.4 cm³</span></p>
                        <p><strong>Diameter:</strong> <span id="containerDiameter">100.0 mm</span></p>
                        <p><strong>Surface Area:</strong> <span id="containerSurfaceArea">471.2 cm²</span></p>
                    </div>
                </div>

                <div class="container-actions">
                    <button type="button" class="btn btn-secondary btn-sm" onclick="resetContainer()">
                        <i class="fas fa-undo"></i> Reset to Default
                    </button>
                    <button type="button" class="btn btn-info btn-sm" onclick="previewContainer()">
                        <i class="fas fa-eye"></i> Preview Container
                    </button>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Submit Button -->
        <button type="submit" id="submitBtn" class="btn" disabled>
            Upload & Process
        </button>

        <!-- Progress Bar -->
        <div id="progressContainer" style="display: none;">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div id="progressText" style="text-align: center; margin-top: 10px;">
                Uploading...
            </div>
        </div>
    </form>
    </div>
    </div>

    <!-- Right Panel: 3D Viewer -->
    <div class="right-panel">
        <div class="card">
<!--?            <h3><i class="fas fa-cube"></i> 3D Model Viewer</h3>-->

            <!-- System Status Banner -->
            <div id="systemStatus" class="system-status" style="display: none;">
                <div class="status-content">
                    <i class="fas fa-info-circle"></i>
                    <span id="statusMessage">Checking system status...</span>
                </div>
            </div>

            <!-- Collision Warning Banner -->
            <div id="collisionWarning" class="collision-warning" style="display: none;">
                <div class="warning-content">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="collisionMessage">Model exceeds container bounds</span>
                    <div class="warning-details" id="collisionDetails"></div>
                </div>
            </div>

            <!-- Container Settings -->
            <div class="container-settings" id="viewer-container-settings">
                <h4>Container Settings <span class="collapse-arrow">▼</span></h4>
                <div class="settings-grid">
                    <div class="form-group">
                        <label for="containerInnerDiameter">Inner Diameter (mm)</label>
                        <input type="number" id="containerInnerDiameter" value="27" min="1" max="200" step="0.1" placeholder="27.0">
                    </div>
                    <div class="form-group">
                        <label for="containerOuterDiameter">Outer Diameter (mm)</label>
                        <input type="number" id="containerOuterDiameter" value="30" min="1" max="250" step="0.1" placeholder="30.0">
                        <div class="wall-thickness-display">
                            <small>Wall Thickness: <span id="wallThickness" class="wall-thickness-value">1.5 mm</span></small>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="containerHeight">Height (mm)</label>
                        <input type="number" id="containerHeight" value="100" min="20" max="300" step="1">
                    </div>
                </div>
            </div>

            <!-- 3D Viewer Container -->
            <div id="threejs-container" class="threejs-container">
                <div id="loading-3d" class="loading-3d">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>Upload a file to view 3D model</p>
                </div>
            </div>

            <!-- Model Controls -->
            <div class="model-controls">
                <h4>Model Position</h4>
                <div class="controls-grid">
                    <div class="control-group">
                        <label>Position (mm)</label>
                        <div class="position-controls">
                            <div class="axis-control">
                                <label>X:</label>
                                <input type="number" id="modelPosX" value="0" step="0.1">
                                <div class="axis-buttons">
                                    <button type="button" class="btn-axis" data-axis="x" data-direction="-1">←</button>
                                    <button type="button" class="btn-axis" data-axis="x" data-direction="1">→</button>
                                </div>
                            </div>
                            <div class="axis-control">
                                <label>Y:</label>
                                <input type="number" id="modelPosY" value="0" step="0.1">
                                <div class="axis-buttons">
                                    <button type="button" class="btn-axis" data-axis="y" data-direction="-1">↓</button>
                                    <button type="button" class="btn-axis" data-axis="y" data-direction="1">↑</button>
                                </div>
                            </div>
                            <div class="axis-control">
                                <label>Z:</label>
                                <input type="number" id="modelPosZ" value="0" step="0.1">
                                <div class="axis-buttons">
                                    <button type="button" class="btn-axis" data-axis="z" data-direction="-1">⬇</button>
                                    <button type="button" class="btn-axis" data-axis="z" data-direction="1">⬆</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="control-group">
                        <label>Scale (%)</label>
                        <div class="scale-controls">
                            <div class="axis-control">
                                <label>Scale:</label>
                                <input type="number" id="modelScale" placeholder="100" step="1" min="1" max="1000" value="100">
                                <span class="unit">%</span>
                            </div>
                            <div class="scale-presets">
                                <button type="button" class="scale-btn" data-scale="50">50%</button>
                                <button type="button" class="scale-btn" data-scale="75">75%</button>
                                <button type="button" class="scale-btn" data-scale="100">100%</button>
                                <button type="button" class="scale-btn" data-scale="125">125%</button>
                                <button type="button" class="scale-btn" data-scale="150">150%</button>
                            </div>
                        </div>
                        <div class="scale-info">
                            <small><i class="fas fa-info-circle"></i> Scale model to fit in container. 100% = original size</small>
                        </div>
                    </div>

                    <div class="control-group">
                        <label>Rotation (degrees)</label>
                        <div class="rotation-controls">
                            <div class="axis-control">
                                <label>X:</label>
                                <input type="number" id="modelRotX" value="0" step="1">
                                <div class="axis-buttons">
                                    <button type="button" class="btn-rotate" data-axis="x" data-direction="-1">↻</button>
                                    <button type="button" class="btn-rotate" data-axis="x" data-direction="1">↺</button>
                                </div>
                            </div>
                            <div class="axis-control">
                                <label>Y:</label>
                                <input type="number" id="modelRotY" value="0" step="1">
                                <div class="axis-buttons">
                                    <button type="button" class="btn-rotate" data-axis="y" data-direction="-1">↻</button>
                                    <button type="button" class="btn-rotate" data-axis="y" data-direction="1">↺</button>
                                </div>
                            </div>
                            <div class="axis-control">
                                <label>Z:</label>
                                <input type="number" id="modelRotZ" value="0" step="1">
                                <div class="axis-buttons">
                                    <button type="button" class="btn-rotate" data-axis="z" data-direction="-1">↻</button>
                                    <button type="button" class="btn-rotate" data-axis="z" data-direction="1">↺</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button type="button" id="resetModel" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Reset Position
                    </button>
                    <button type="button" id="centerModel" class="btn btn-primary">
                        <i class="fas fa-crosshairs"></i> Center Model
                    </button>
                    <button type="button" id="autoReorient" class="btn btn-warning">
                        <i class="fas fa-magic"></i> Auto Reorient
                    </button>
                    <button type="button" id="debugPosition" class="btn btn-info">
                        <i class="fas fa-bug"></i> Debug Position
                    </button>
                </div>

                <!-- Keyboard Controls Help -->
                <div class="keyboard-controls-help">
                    <h5><i class="fas fa-keyboard"></i> Keyboard Controls & Auto Features</h5>
                    <div class="controls-grid">
                        <div class="control-section">
                            <strong>Rotation:</strong>
                            <div class="control-item">
                                <span class="key-combo">← →</span>
                                <span>Rotate around vertical axis</span>
                            </div>
                            <div class="control-item">
                                <span class="key-combo">↑ ↓</span>
                                <span>Rotate around horizontal axis</span>
                            </div>
                        </div>
                        <div class="control-section">
                            <strong>Translation:</strong>
                            <div class="control-item">
                                <span class="key-combo">Ctrl + ← →</span>
                                <span>Move left/right</span>
                            </div>
                            <div class="control-item">
                                <span class="key-combo">Ctrl + ↑ ↓</span>
                                <span>Move up/down</span>
                            </div>
                        </div>
                        <div class="control-section">
                            <strong>Auto Features:</strong>
                            <div class="control-item">
                                <span class="key-combo">Auto</span>
                                <span>Reorient on upload</span>
                            </div>
                            <div class="control-item">
                                <span class="key-combo">Manual</span>
                                <span>Click Auto Reorient button</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Geometry Analysis Panel -->
            <div class="geometry-analysis">
                <h4>Geometry Analysis <span class="collapse-arrow">▼</span></h4>
                <div class="analysis-grid">
                    <div class="analysis-section">
                        <h5>Dimensions (mm)</h5>
                        <div class="dimension-grid">
                            <div class="dimension-item">
                                <label>Length (X):</label>
                                <span id="modelLength">-</span>
                            </div>
                            <div class="dimension-item">
                                <label>Width (Z):</label>
                                <span id="modelWidth">-</span>
                            </div>
                            <div class="dimension-item">
                                <label>Height (Y):</label>
                                <span id="modelHeight">-</span>
                            </div>
                            <div class="dimension-item">
                                <label>Min Inner Diameter:</label>
                                <span id="modelMinInnerDiameter">-</span>
                            </div>
                        </div>
                    </div>

                    <div class="analysis-section">
                        <h5>Cross-Section Analysis</h5>
                        <div class="cross-section-grid">
                            <div class="cross-section-item">
                                <label>Mean Area:</label>
                                <span id="crossSectionMean">-</span> mm²
                            </div>
                            <div class="cross-section-item">
                                <label>Max Area:</label>
                                <span id="crossSectionMax">-</span> mm²
                            </div>
                            <div class="cross-section-item">
                                <label>Min Area:</label>
                                <span id="crossSectionMin">-</span> mm²
                            </div>
                            <div class="cross-section-item">
                                <label>Variance:</label>
                                <span id="crossSectionVariance">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Three.js Library -->
<script type="importmap">
{
  "imports": {
    "three": "https://unpkg.com/three@0.158.0/build/three.module.js",
    "three/addons/": "https://unpkg.com/three@0.158.0/examples/jsm/"
  }
}
</script>
<script type="module">
import * as THREE from 'three';
import { STLLoader } from 'three/addons/loaders/STLLoader.js';
import { OBJLoader } from 'three/addons/loaders/OBJLoader.js';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

// Make THREE available globally
window.THREE = THREE;
window.STLLoader = STLLoader;
window.OBJLoader = OBJLoader;
window.OrbitControls = OrbitControls;
</script>

<script>
// 3D Viewer Class
class Model3DViewer {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.model = null;
        this.container3D = null;
        this.coordinateSystem = null;

        // Model transform parameters
        this.modelPosition = { x: 0, y: 0, z: 0 };
        this.modelRotation = { x: 0, y: 0, z: 0 };
        this.modelScale = 100; // Scale in percentage (100% = original size)

        // Container parameters (matching backend defaults)
        this.containerParams = {
            innerDiameter: 27,  // 默认内径27mm
            outerDiameter: 30,  // 默认外径30mm
            height: 100
        };

        this.init();
        this.setupEventListeners();
    }

    init() {
        // Create scene
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0xf0f0f0);

        // Create camera
        this.camera = new THREE.PerspectiveCamera(
            75,
            this.container.clientWidth / this.container.clientHeight,
            0.1,
            1000
        );
        this.camera.position.set(100, 100, 100);

        // Create renderer
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

        // Clear loading message and add renderer
        this.container.innerHTML = '';
        this.container.appendChild(this.renderer.domElement);

        // Add controls
        this.controls = new OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;

        // Add lights
        this.setupLights();

        // Add coordinate system
        this.addCoordinateSystem();

        // Add container
        this.addContainer();

        // Start render loop
        this.animate();

        // Handle window resize
        window.addEventListener('resize', () => this.onWindowResize());
    }

    setupLights() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);

        // Directional light
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        this.scene.add(directionalLight);

        // Point light
        const pointLight = new THREE.PointLight(0xffffff, 0.5);
        pointLight.position.set(-50, 50, 50);
        this.scene.add(pointLight);
    }

    addCoordinateSystem() {
        // Create coordinate system axes
        const axesHelper = new THREE.AxesHelper(50);
        this.scene.add(axesHelper);

        // Add grid
        const gridHelper = new THREE.GridHelper(200, 20, 0x888888, 0xcccccc);
        this.scene.add(gridHelper);
    }

    addContainer() {
        // Remove existing container
        if (this.container3D) {
            this.scene.remove(this.container3D);
        }

        this.container3D = new THREE.Group();

        // Create outer cylinder
        const outerGeometry = new THREE.CylinderGeometry(
            this.containerParams.outerDiameter / 2,
            this.containerParams.outerDiameter / 2,
            this.containerParams.height,
            32
        );
        const outerMaterial = new THREE.MeshLambertMaterial({
            color: 0x3498db,
            transparent: true,
            opacity: 0.3
        });
        const outerCylinder = new THREE.Mesh(outerGeometry, outerMaterial);

        // Create inner cylinder (hollow part)
        const innerGeometry = new THREE.CylinderGeometry(
            this.containerParams.innerDiameter / 2,
            this.containerParams.innerDiameter / 2,
            this.containerParams.height + 1,
            32
        );
        const innerMaterial = new THREE.MeshLambertMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.1
        });
        const innerCylinder = new THREE.Mesh(innerGeometry, innerMaterial);

        // Create wireframe for better visibility
        const wireframeGeometry = new THREE.CylinderGeometry(
            this.containerParams.outerDiameter / 2,
            this.containerParams.outerDiameter / 2,
            this.containerParams.height,
            32
        );
        const wireframeMaterial = new THREE.WireframeGeometry(wireframeGeometry);
        const wireframe = new THREE.LineSegments(wireframeMaterial, new THREE.LineBasicMaterial({ color: 0x2980b9 }));

        this.container3D.add(outerCylinder);
        this.container3D.add(innerCylinder);
        this.container3D.add(wireframe);

        // Position container
        this.container3D.position.y = this.containerParams.height / 2;

        this.scene.add(this.container3D);
    }

    loadSTLModel(file) {
        const loader = new STLLoader();
        const reader = new FileReader();

        reader.onload = (event) => {
            try {
                const geometry = loader.parse(event.target.result);

                // Remove existing model
                if (this.model) {
                    this.scene.remove(this.model);
                }

                // Create material
                const material = new THREE.MeshLambertMaterial({ color: 0xe74c3c });

                // Create mesh
                this.model = new THREE.Mesh(geometry, material);
                this.model.castShadow = true;
                this.model.receiveShadow = true;

                // Center and scale model
                this.centerModel();

                this.scene.add(this.model);

                // Check for collisions after loading
                this.checkCollision();

                // Analyze geometry after loading
                this.analyzeGeometry();

                // Auto-reorient model for optimal placement
                setTimeout(() => {
                    this.autoReorientModel();
                }, 500); // Small delay to ensure geometry analysis is complete

                console.log('STL model loaded successfully');

            } catch (error) {
                console.error('Error loading STL model:', error);
                alert('Error loading STL file. Please check the file format.');
            }
        };

        reader.readAsArrayBuffer(file);
    }

    loadOBJModel(file) {
        console.log('Loading OBJ file:', file.name, 'Size:', file.size, 'bytes');

        const loader = new OBJLoader();
        const reader = new FileReader();

        reader.onload = (event) => {
            try {
                const content = event.target.result;
                console.log('OBJ file content length:', content.length);

                // Basic OBJ format validation
                if (!this.validateOBJContent(content)) {
                    throw new Error('Invalid OBJ file format. File does not contain valid OBJ data.');
                }

                const object = loader.parse(content);
                console.log('OBJ parsed successfully, object:', object);

                // Remove existing model
                if (this.model) {
                    this.scene.remove(this.model);
                }

                // Find the first mesh in the object and extract its geometry
                let geometry = null;
                let meshCount = 0;

                object.traverse((child) => {
                    if (child.isMesh) {
                        meshCount++;
                        if (child.geometry && !geometry) {
                            geometry = child.geometry;
                            console.log('Found mesh with geometry:', {
                                vertices: geometry.attributes.position?.count || 0,
                                faces: geometry.index ? geometry.index.count / 3 : 0
                            });
                        }
                    }
                });

                console.log(`Found ${meshCount} meshes in OBJ file`);

                if (geometry) {
                    // Validate geometry
                    if (!geometry.attributes.position || geometry.attributes.position.count === 0) {
                        throw new Error('OBJ file contains no vertex data');
                    }

                    // Compute normals if not present
                    if (!geometry.attributes.normal) {
                        geometry.computeVertexNormals();
                        console.log('Computed vertex normals for OBJ model');
                    }

                    // Create material
                    const material = new THREE.MeshLambertMaterial({ color: 0x3498db });

                    // Create mesh
                    this.model = new THREE.Mesh(geometry, material);
                    this.model.castShadow = true;
                    this.model.receiveShadow = true;

                    // Center and scale model
                    this.centerModel();

                    this.scene.add(this.model);

                    // Check for collisions after loading
                    this.checkCollision();

                    // Analyze geometry after loading
                    this.analyzeGeometry();

                    // Auto-reorient model for optimal placement
                    setTimeout(() => {
                        this.autoReorientModel();
                    }, 500); // Small delay to ensure geometry analysis is complete

                    console.log('OBJ model loaded successfully');
                } else {
                    throw new Error(`No valid mesh found in OBJ file. Found ${meshCount} meshes but none contained geometry.`);
                }

            } catch (error) {
                console.error('Error loading OBJ model:', error);
                const errorMsg = error.message || 'Unknown error occurred while loading OBJ file';

                // 提供更详细的错误信息和解决建议
                let detailedError = `Error loading OBJ file: ${errorMsg}\n\n`;
                detailedError += `Troubleshooting tips:\n`;
                detailedError += `• Ensure the file is a valid OBJ format\n`;
                detailedError += `• Check that the file contains vertex data (v x y z)\n`;
                detailedError += `• Verify the file is not corrupted\n`;
                detailedError += `• Try exporting the OBJ file again from your 3D software\n`;
                detailedError += `• Check the browser console for detailed error information`;

                alert(detailedError);

                // 记录详细的调试信息
                console.group('OBJ Loading Error Details');
                console.error('Error object:', error);
                console.error('Error stack:', error.stack);
                console.error('File name:', file?.name || 'Unknown');
                console.error('File size:', file?.size || 'Unknown');
                console.groupEnd();
            }
        };

        reader.onerror = (error) => {
            console.error('FileReader error:', error);
            alert('Error reading OBJ file. The file may be corrupted.');
        };

        // Read as text with UTF-8 encoding
        reader.readAsText(file, 'UTF-8');
    }

    validateOBJContent(content) {
        // Basic OBJ format validation
        if (!content || typeof content !== 'string') {
            console.error('OBJ content is empty or not a string');
            return false;
        }

        // Check for basic OBJ elements
        const lines = content.split('\n');
        let hasVertices = false;
        let hasFaces = false;
        let validLineCount = 0;

        for (let i = 0; i < Math.min(lines.length, 1000); i++) { // Check first 1000 lines
            const line = lines[i].trim();

            if (line.length === 0 || line.startsWith('#')) {
                continue; // Skip empty lines and comments
            }

            validLineCount++;

            if (line.startsWith('v ')) {
                hasVertices = true;
                // Validate vertex format: v x y z
                const parts = line.split(/\s+/);
                if (parts.length >= 4) {
                    const x = parseFloat(parts[1]);
                    const y = parseFloat(parts[2]);
                    const z = parseFloat(parts[3]);
                    if (isNaN(x) || isNaN(y) || isNaN(z)) {
                        console.error('Invalid vertex data in OBJ file:', line);
                        return false;
                    }
                }
            } else if (line.startsWith('f ')) {
                hasFaces = true;
                // Basic face validation
                const parts = line.split(/\s+/);
                if (parts.length < 4) { // At least 3 vertices for a face
                    console.error('Invalid face data in OBJ file:', line);
                    return false;
                }
            }
        }

        console.log('OBJ validation results:', {
            hasVertices,
            hasFaces,
            validLineCount,
            totalLines: lines.length
        });

        if (!hasVertices) {
            console.error('OBJ file contains no vertex data');
            return false;
        }

        if (validLineCount === 0) {
            console.error('OBJ file contains no valid data');
            return false;
        }

        return true;
    }

    centerModel() {
        if (!this.model) return;

        console.log('Centering model...');

        // Reset rotation first
        this.modelRotation = { x: 0, y: 0, z: 0 };

        // Position the model correctly in the container
        this.positionModelInContainer();

        // Update controls
        this.updateControlInputs();

        // Check for collisions after centering
        this.checkCollision();

        // Analyze geometry after centering
        this.analyzeGeometry();

        console.log('Model centered successfully');
    }

    updateModelTransform() {
        if (!this.model) return;

        // Update position
        this.model.position.set(
            this.modelPosition.x,
            this.modelPosition.y,
            this.modelPosition.z
        );

        // Update rotation (convert degrees to radians)
        this.model.rotation.set(
            THREE.MathUtils.degToRad(this.modelRotation.x),
            THREE.MathUtils.degToRad(this.modelRotation.y),
            THREE.MathUtils.degToRad(this.modelRotation.z)
        );

        // Update scale (convert percentage to decimal)
        const scaleValue = this.modelScale / 100;
        this.model.scale.set(scaleValue, scaleValue, scaleValue);

        this.render();

        // Check for collisions after transform
        this.checkCollision();

        // Analyze geometry after transform
        this.analyzeGeometry();
    }

    updateControlInputs() {
        document.getElementById('modelPosX').value = this.modelPosition.x.toFixed(1);
        document.getElementById('modelPosY').value = this.modelPosition.y.toFixed(1);
        document.getElementById('modelPosZ').value = this.modelPosition.z.toFixed(1);
        document.getElementById('modelRotX').value = this.modelRotation.x.toFixed(0);
        document.getElementById('modelRotY').value = this.modelRotation.y.toFixed(0);
        document.getElementById('modelRotZ').value = this.modelRotation.z.toFixed(0);
        document.getElementById('modelScale').value = this.modelScale.toFixed(0);

        // Update scale button states
        this.updateScaleButtonStates();
    }

    updateScaleButtonStates() {
        const scaleButtons = document.querySelectorAll('.scale-btn');
        scaleButtons.forEach(btn => {
            const btnScale = parseInt(btn.dataset.scale);
            if (btnScale === this.modelScale) {
                btn.classList.add('active');
            } else {
                btn.classList.remove('active');
            }
        });
    }

    updateContainerParams() {
        const innerDiameterInput = document.getElementById('containerInnerDiameter');
        const outerDiameterInput = document.getElementById('containerOuterDiameter');
        const heightInput = document.getElementById('containerHeight');

        if (!innerDiameterInput || !outerDiameterInput || !heightInput) return;

        this.containerParams.innerDiameter = parseFloat(innerDiameterInput.value);
        this.containerParams.outerDiameter = parseFloat(outerDiameterInput.value);
        this.containerParams.height = parseFloat(heightInput.value);

        // Validate parameters
        if (this.containerParams.innerDiameter >= this.containerParams.outerDiameter) {
            this.containerParams.outerDiameter = this.containerParams.innerDiameter + 10;
            outerDiameterInput.value = this.containerParams.outerDiameter;
        }

        this.addContainer();
        this.checkCollision(); // Check collision after container update
    }

    calculateWallThickness() {
        const outerDiameterInput = document.getElementById('containerOuterDiameter');
        const innerDiameterInput = document.getElementById('containerInnerDiameter');
        const wallThicknessSpan = document.getElementById('wallThickness');

        if (!outerDiameterInput || !innerDiameterInput || !wallThicknessSpan) return;

        const outerDiameter = parseFloat(outerDiameterInput.value);
        const innerDiameter = parseFloat(innerDiameterInput.value);

        if (!isNaN(outerDiameter) && !isNaN(innerDiameter)) {
            if (innerDiameter >= outerDiameter) {
                wallThicknessSpan.textContent = "Invalid";
                wallThicknessSpan.style.color = "#e74c3c";
                wallThicknessSpan.title = "Inner diameter must be less than outer diameter";
            } else {
                const wallThickness = (outerDiameter - innerDiameter) / 2;
                wallThicknessSpan.textContent = `${wallThickness.toFixed(1)} mm`;
                wallThicknessSpan.style.color = "#28a745";
                wallThicknessSpan.title = `Wall thickness: ${wallThickness.toFixed(1)}mm`;
            }
        } else if (!isNaN(outerDiameter) || !isNaN(innerDiameter)) {
            wallThicknessSpan.textContent = "Calculating...";
            wallThicknessSpan.style.color = "#6c757d";
            wallThicknessSpan.title = "Enter both diameters to calculate";
        } else {
            wallThicknessSpan.textContent = "1.5 mm";
            wallThicknessSpan.style.color = "#6c757d";
            wallThicknessSpan.title = "Default wall thickness";
        }
    }

    checkCollision() {
        if (!this.model) {
            this.hideCollisionWarning();
            return false;
        }

        // Get model's world bounding box
        const modelBox = new THREE.Box3().setFromObject(this.model);
        const modelSize = modelBox.getSize(new THREE.Vector3());
        const modelCenter = modelBox.getCenter(new THREE.Vector3());

        // Container parameters
        const containerRadius = this.containerParams.innerDiameter / 2;
        const containerHeight = this.containerParams.height;

        // Check collisions
        const collisions = [];

        // Check height collision (Y-axis)
        if (modelBox.min.y < 0) {
            collisions.push(`Model extends ${Math.abs(modelBox.min.y).toFixed(1)}mm below container bottom`);
        }
        if (modelBox.max.y > containerHeight) {
            collisions.push(`Model extends ${(modelBox.max.y - containerHeight).toFixed(1)}mm above container top`);
        }

        // Check radial collision (XZ plane)
        // For simplicity, we'll check if the model's bounding box fits in the cylinder
        const maxRadialDistance = Math.sqrt(
            Math.max(
                modelBox.min.x * modelBox.min.x + modelBox.min.z * modelBox.min.z,
                modelBox.min.x * modelBox.min.x + modelBox.max.z * modelBox.max.z,
                modelBox.max.x * modelBox.max.x + modelBox.min.z * modelBox.min.z,
                modelBox.max.x * modelBox.max.x + modelBox.max.z * modelBox.max.z
            )
        );

        if (maxRadialDistance > containerRadius) {
            const overflow = maxRadialDistance - containerRadius;
            collisions.push(`Model extends ${overflow.toFixed(1)}mm beyond container walls`);
        }

        // Update UI based on collision status
        if (collisions.length > 0) {
            this.showCollisionWarning(collisions);
            return true;
        } else {
            this.hideCollisionWarning();
            return false;
        }
    }

    showCollisionWarning(collisions) {
        const warningElement = document.getElementById('collisionWarning');
        const messageElement = document.getElementById('collisionMessage');
        const detailsElement = document.getElementById('collisionDetails');

        messageElement.textContent = 'Model exceeds container bounds';
        detailsElement.innerHTML = collisions.map(collision => `• ${collision}`).join('<br>');

        warningElement.style.display = 'block';

        // Update submit button state
        this.updateSubmitButtonState();
    }

    hideCollisionWarning() {
        const warningElement = document.getElementById('collisionWarning');
        warningElement.style.display = 'none';

        // Update submit button state
        this.updateSubmitButtonState();
    }

    updateSubmitButtonState() {
        const submitBtn = document.getElementById('submitBtn');
        const hasCollision = document.getElementById('collisionWarning').style.display !== 'none';
        const fileInput = document.getElementById('fileInput');
        const hasFile = fileInput && fileInput.files && fileInput.files.length > 0;

        if (submitBtn) {
            if (hasCollision) {
                // Allow submission but show warning style
                submitBtn.disabled = false;
                submitBtn.textContent = 'Upload & Process (Warning: Model Exceeds Container)';
                submitBtn.style.backgroundColor = '#f39c12';
                submitBtn.style.cursor = 'pointer';
                submitBtn.title = 'Model exceeds container bounds. Click to submit with confirmation.';
            } else if (hasFile) {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Upload & Process';
                submitBtn.style.backgroundColor = '';
                submitBtn.style.cursor = '';
                submitBtn.title = '';
            } else {
                submitBtn.disabled = true;
                submitBtn.textContent = 'Upload & Process';
                submitBtn.style.backgroundColor = '';
                submitBtn.style.cursor = '';
                submitBtn.title = '';
            }
        }
    }

    analyzeGeometry() {
        if (!this.model) {
            this.clearGeometryAnalysis();
            return;
        }

        console.log('Starting geometry analysis...');

        // Get model's world bounding box
        const modelBox = new THREE.Box3().setFromObject(this.model);
        const modelSize = modelBox.getSize(new THREE.Vector3());

        // Basic dimensions
        const dimensions = {
            length: modelSize.x,
            width: modelSize.z,
            height: modelSize.y
        };

        // Perform cross-section analysis
        const crossSectionData = this.analyzeCrossSections();

        // Calculate minimum inner diameter
        const minInnerDiameter = this.calculateMinInnerDiameter(crossSectionData);

        // Update UI
        this.updateGeometryDisplay(dimensions, crossSectionData, minInnerDiameter);

        console.log('Geometry analysis completed');
    }

    analyzeCrossSections() {
        if (!this.model || !this.model.geometry) {
            return { areas: [], mean: 0, max: 0, min: 0, variance: 0 };
        }

        const geometry = this.model.geometry;
        const modelBox = new THREE.Box3().setFromObject(this.model);

        const sliceCount = 50; // Number of slices for analysis
        const yMin = modelBox.min.y;
        const yMax = modelBox.max.y;
        const sliceHeight = (yMax - yMin) / sliceCount;

        const areas = [];

        // Analyze each slice
        for (let i = 0; i < sliceCount; i++) {
            const y = yMin + (i + 0.5) * sliceHeight;
            const area = this.calculateSliceArea(geometry, y);
            areas.push(area);
        }

        // Filter out zero areas for statistics
        const nonZeroAreas = areas.filter(area => area > 0);

        if (nonZeroAreas.length === 0) {
            return { areas: [], mean: 0, max: 0, min: 0, variance: 0 };
        }

        // Calculate statistics
        const mean = nonZeroAreas.reduce((sum, area) => sum + area, 0) / nonZeroAreas.length;
        const max = Math.max(...nonZeroAreas);
        const min = Math.min(...nonZeroAreas);

        // Calculate variance
        const variance = nonZeroAreas.reduce((sum, area) => {
            return sum + Math.pow(area - mean, 2);
        }, 0) / nonZeroAreas.length;

        return { areas: nonZeroAreas, mean, max, min, variance };
    }

    calculateSliceArea(geometry, y) {
        // Simplified cross-section area calculation
        // This is an approximation based on the geometry's bounding box at the given Y level

        const modelBox = new THREE.Box3().setFromObject(this.model);
        const tolerance = 0.1; // Tolerance for Y-level matching

        // For a more accurate calculation, we would need to:
        // 1. Intersect the geometry with a plane at Y level
        // 2. Calculate the area of the resulting 2D shape
        // For now, we'll use a simplified approach based on the model's profile

        if (y < modelBox.min.y || y > modelBox.max.y) {
            return 0;
        }

        // Simplified calculation: assume the cross-section is roughly circular/rectangular
        // and varies linearly with height
        const heightRatio = (y - modelBox.min.y) / (modelBox.max.y - modelBox.min.y);
        const maxArea = (modelBox.max.x - modelBox.min.x) * (modelBox.max.z - modelBox.min.z);

        // Simple approximation: area varies as a function of height
        // This could be improved with actual mesh intersection
        const area = maxArea * Math.sin(heightRatio * Math.PI) * 0.7; // Approximate shape factor

        return Math.max(0, area);
    }

    calculateMinInnerDiameter(crossSectionData) {
        // Simplified calculation for minimum inner diameter
        // In a real implementation, this would analyze the geometry for holes/cavities

        if (crossSectionData.areas.length === 0) {
            return 0;
        }

        // For now, we'll estimate based on the minimum cross-sectional area
        // assuming it represents a circular hole
        const minArea = crossSectionData.min;
        const estimatedRadius = Math.sqrt(minArea / Math.PI);
        const estimatedDiameter = estimatedRadius * 2;

        // Return a reasonable estimate (this is a simplified approach)
        return Math.max(0, estimatedDiameter * 0.1); // Scale factor for inner diameter estimation
    }

    updateGeometryDisplay(dimensions, crossSectionData, minInnerDiameter) {
        // Update dimension displays
        document.getElementById('modelLength').textContent = dimensions.length.toFixed(2);
        document.getElementById('modelWidth').textContent = dimensions.width.toFixed(2);
        document.getElementById('modelHeight').textContent = dimensions.height.toFixed(2);
        document.getElementById('modelMinInnerDiameter').textContent = minInnerDiameter.toFixed(2);

        // Update cross-section analysis displays
        document.getElementById('crossSectionMean').textContent = crossSectionData.mean.toFixed(2);
        document.getElementById('crossSectionMax').textContent = crossSectionData.max.toFixed(2);
        document.getElementById('crossSectionMin').textContent = crossSectionData.min.toFixed(2);
        document.getElementById('crossSectionVariance').textContent = crossSectionData.variance.toFixed(2);
    }

    clearGeometryAnalysis() {
        // Clear all geometry analysis displays
        const elements = [
            'modelLength', 'modelWidth', 'modelHeight', 'modelMinInnerDiameter',
            'crossSectionMean', 'crossSectionMax', 'crossSectionMin', 'crossSectionVariance'
        ];

        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = '-';
            }
        });
    }

    setupEventListeners() {
        // Keyboard controls for model manipulation
        this.setupKeyboardControls();

        // Geometry analysis panel collapse functionality
        const geometryAnalysisPanel = document.querySelector('.geometry-analysis');
        if (geometryAnalysisPanel) {
            const geometryTitle = geometryAnalysisPanel.querySelector('h4');
            if (geometryTitle) {
                geometryTitle.addEventListener('click', function() {
                    geometryAnalysisPanel.classList.toggle('collapsed');
                });
            }
        }

        // Container parameter changes
        ['containerInnerDiameter', 'containerOuterDiameter', 'containerHeight'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', () => {
                    this.updateContainerParams();
                    this.calculateWallThickness();
                });
                element.addEventListener('input', () => {
                    this.calculateWallThickness();
                });
            }
        });

        // 初始化壁厚计算
        this.calculateWallThickness();

        // Position input changes
        ['modelPosX', 'modelPosY', 'modelPosZ'].forEach((id, index) => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', (e) => {
                    const axes = ['x', 'y', 'z'];
                    this.modelPosition[axes[index]] = parseFloat(e.target.value);
                    this.updateModelTransform();
                });
            }
        });

        // Rotation input changes
        ['modelRotX', 'modelRotY', 'modelRotZ'].forEach((id, index) => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', (e) => {
                    const axes = ['x', 'y', 'z'];
                    this.modelRotation[axes[index]] = parseFloat(e.target.value);
                    this.updateModelTransform();
                });
            }
        });

        // Scale input changes
        const scaleInput = document.getElementById('modelScale');
        if (scaleInput) {
            scaleInput.addEventListener('change', (e) => {
                this.modelScale = parseFloat(e.target.value);
                this.updateModelTransform();
                this.updateScaleButtonStates();
            });
        }

        // Scale preset buttons
        document.querySelectorAll('.scale-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.modelScale = parseInt(btn.dataset.scale);
                this.updateControlInputs();
                this.updateModelTransform();
            });
        });

        // Axis movement buttons
        document.querySelectorAll('.btn-axis').forEach(btn => {
            btn.addEventListener('click', () => {
                const axis = btn.dataset.axis;
                const direction = parseInt(btn.dataset.direction);
                const step = 1; // 1mm step

                this.modelPosition[axis] += direction * step;
                this.updateControlInputs();
                this.updateModelTransform();
            });
        });

        // Rotation buttons
        document.querySelectorAll('.btn-rotate').forEach(btn => {
            btn.addEventListener('click', () => {
                const axis = btn.dataset.axis;
                const direction = parseInt(btn.dataset.direction);
                const step = 15; // 15 degree step

                this.modelRotation[axis] += direction * step;
                this.updateControlInputs();
                this.updateModelTransform();
            });
        });

        // Action buttons
        const resetBtn = document.getElementById('resetModel');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.modelPosition = { x: 0, y: 0, z: 0 };
                this.modelRotation = { x: 0, y: 0, z: 0 };
                this.modelScale = 100; // Reset to 100%
                this.updateControlInputs();
                this.updateModelTransform();
            });
        }

        const centerBtn = document.getElementById('centerModel');
        if (centerBtn) {
            centerBtn.addEventListener('click', () => {
                this.centerModel();
            });
        }

        const autoReorientBtn = document.getElementById('autoReorient');
        if (autoReorientBtn) {
            autoReorientBtn.addEventListener('click', () => {
                this.autoReorientModel();
            });
        }

        const debugBtn = document.getElementById('debugPosition');
        if (debugBtn) {
            debugBtn.addEventListener('click', () => {
                this.debugModelPosition();
            });
        }

        const applyBtn = document.getElementById('applyChanges');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.applyChangesAndRender();
            });
        }
    }

    setupKeyboardControls() {
        // Keyboard control settings
        this.keyboardSettings = {
            rotationStep: 5,    // degrees
            translationStep: 1  // mm
        };

        // Add keyboard event listener
        document.addEventListener('keydown', (event) => {
            // Only handle keyboard controls when 3D viewer area is focused or no input is focused
            const activeElement = document.activeElement;
            const isInputFocused = activeElement && (
                activeElement.tagName === 'INPUT' ||
                activeElement.tagName === 'TEXTAREA' ||
                activeElement.contentEditable === 'true'
            );

            // Skip if an input field is focused
            if (isInputFocused) {
                return;
            }

            // Check if we have a model to control
            if (!this.model) {
                return;
            }

            let handled = false;

            // Handle rotation controls (arrow keys without Ctrl)
            if (!event.ctrlKey) {
                switch (event.key) {
                    case 'ArrowLeft':
                        // Rotate left around Y-axis (vertical axis)
                        this.modelRotation.y -= this.keyboardSettings.rotationStep;
                        handled = true;
                        break;
                    case 'ArrowRight':
                        // Rotate right around Y-axis (vertical axis)
                        this.modelRotation.y += this.keyboardSettings.rotationStep;
                        handled = true;
                        break;
                    case 'ArrowUp':
                        // Rotate up around X-axis (horizontal axis)
                        this.modelRotation.x -= this.keyboardSettings.rotationStep;
                        handled = true;
                        break;
                    case 'ArrowDown':
                        // Rotate down around X-axis (horizontal axis)
                        this.modelRotation.x += this.keyboardSettings.rotationStep;
                        handled = true;
                        break;
                }
            }
            // Handle translation controls (Ctrl + arrow keys)
            else if (event.ctrlKey) {
                switch (event.key) {
                    case 'ArrowLeft':
                        // Move left (negative X)
                        this.modelPosition.x -= this.keyboardSettings.translationStep;
                        handled = true;
                        break;
                    case 'ArrowRight':
                        // Move right (positive X)
                        this.modelPosition.x += this.keyboardSettings.translationStep;
                        handled = true;
                        break;
                    case 'ArrowUp':
                        // Move up (positive Y)
                        this.modelPosition.y += this.keyboardSettings.translationStep;
                        handled = true;
                        break;
                    case 'ArrowDown':
                        // Move down (negative Y)
                        this.modelPosition.y -= this.keyboardSettings.translationStep;
                        handled = true;
                        break;
                }
            }

            // If we handled a key, update the model and UI
            if (handled) {
                event.preventDefault(); // Prevent default browser behavior
                this.updateModelTransform();
                this.updateControlInputs();

                // Show brief feedback
                this.showKeyboardFeedback(event);
            }
        });
    }

    showKeyboardFeedback(event) {
        // Create or update feedback element
        let feedback = document.getElementById('keyboardFeedback');
        if (!feedback) {
            feedback = document.createElement('div');
            feedback.id = 'keyboardFeedback';
            feedback.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(52, 152, 219, 0.9);
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 600;
                z-index: 1000;
                transition: opacity 0.3s ease;
            `;
            document.body.appendChild(feedback);
        }

        // Update feedback text
        let action = '';
        if (event.ctrlKey) {
            const direction = {
                'ArrowLeft': 'Left',
                'ArrowRight': 'Right',
                'ArrowUp': 'Up',
                'ArrowDown': 'Down'
            }[event.key];
            action = `Move ${direction}`;
        } else {
            const rotation = {
                'ArrowLeft': 'Rotate Left',
                'ArrowRight': 'Rotate Right',
                'ArrowUp': 'Rotate Up',
                'ArrowDown': 'Rotate Down'
            }[event.key];
            action = rotation;
        }

        feedback.textContent = action;
        feedback.style.opacity = '1';

        // Hide feedback after 1 second
        clearTimeout(this.feedbackTimeout);
        this.feedbackTimeout = setTimeout(() => {
            feedback.style.opacity = '0';
        }, 1000);
    }

    autoReorientModel() {
        if (!this.model) {
            alert('Please load a model first.');
            return;
        }

        console.log('Starting auto-reorientation...');

        // Show progress feedback
        this.showReorientationProgress('Analyzing model geometry...');

        // Step 1: Analyze current model dimensions in all orientations
        const orientationAnalysis = this.analyzeAllOrientations();

        // Step 2: Find optimal orientation for container compatibility
        const optimalOrientation = this.findOptimalOrientation(orientationAnalysis);

        // Step 3: Apply the optimal orientation
        this.applyOptimalOrientation(optimalOrientation);

        console.log('Auto-reorientation completed');
        this.hideReorientationProgress();
    }

    analyzeAllOrientations() {
        const orientations = [];
        const testRotations = [
            { x: 0, y: 0, z: 0 },     // Original
            { x: 90, y: 0, z: 0 },    // X-axis 90°
            { x: 0, y: 90, z: 0 },    // Y-axis 90°
            { x: 0, y: 0, z: 90 },    // Z-axis 90°
            { x: 90, y: 90, z: 0 },   // X+Y 90°
            { x: 90, y: 0, z: 90 },   // X+Z 90°
            { x: 0, y: 90, z: 90 },   // Y+Z 90°
            { x: 180, y: 0, z: 0 },   // X-axis 180°
            { x: 0, y: 180, z: 0 },   // Y-axis 180°
            { x: 0, y: 0, z: 180 },   // Z-axis 180°
        ];

        // Store original rotation
        const originalRotation = { ...this.modelRotation };

        testRotations.forEach((rotation, index) => {
            // Apply test rotation
            this.modelRotation = { ...rotation };
            this.updateModelTransform();

            // Temporarily center the model for accurate dimension calculation
            const tempPosition = { ...this.modelPosition };
            this.modelPosition = { x: 0, y: 0, z: 0 };
            this.updateModelTransform();

            // Get dimensions and cross-section analysis
            const modelBox = new THREE.Box3().setFromObject(this.model);
            const dimensions = {
                length: modelBox.max.x - modelBox.min.x,
                width: modelBox.max.z - modelBox.min.z,
                height: modelBox.max.y - modelBox.min.y
            };

            // Calculate radial dimension (max of length and width)
            const radialDimension = Math.max(dimensions.length, dimensions.width);
            const aspectRatio = radialDimension / dimensions.height;

            // Analyze cross-sections for this orientation
            const crossSectionData = this.analyzeCrossSections();

            orientations.push({
                index,
                rotation: { ...rotation },
                dimensions,
                radialDimension,
                aspectRatio,
                crossSectionData,
                containerCompatibility: this.calculateContainerCompatibility(dimensions),
                crossSectionScore: this.calculateCrossSectionScore(crossSectionData)
            });

            // Restore original position
            this.modelPosition = tempPosition;
        });

        // Restore original rotation
        this.modelRotation = originalRotation;
        this.updateModelTransform();

        return orientations;
    }

    calculateContainerCompatibility(dimensions) {
        const containerRadius = this.containerParams.innerDiameter / 2;
        const containerHeight = this.containerParams.height;

        // Check if model fits in container
        const radialDimension = Math.max(dimensions.length, dimensions.width);
        const fitsRadially = radialDimension <= containerRadius * 2;
        const fitsVertically = dimensions.height <= containerHeight;

        // Calculate utilization efficiency
        const radialUtilization = radialDimension / (containerRadius * 2);
        const verticalUtilization = dimensions.height / containerHeight;
        const overallUtilization = (radialUtilization + verticalUtilization) / 2;

        return {
            fits: fitsRadially && fitsVertically,
            radialFit: fitsRadially,
            verticalFit: fitsVertically,
            radialUtilization,
            verticalUtilization,
            overallUtilization,
            score: fitsRadially && fitsVertically ? overallUtilization : 0
        };
    }

    calculateCrossSectionScore(crossSectionData) {
        if (crossSectionData.areas.length === 0) {
            return 0;
        }

        const variance = crossSectionData.variance;
        const range = crossSectionData.max - crossSectionData.min;
        const mean = crossSectionData.mean;

        // Lower variance and range are better (more uniform cross-sections)
        // Normalize by mean to make it relative
        const normalizedVariance = variance / (mean * mean);
        const normalizedRange = range / mean;

        // Score: lower is better, so we invert it
        const varianceScore = 1 / (1 + normalizedVariance);
        const rangeScore = 1 / (1 + normalizedRange);

        return (varianceScore + rangeScore) / 2;
    }

    findOptimalOrientation(orientations) {
        // Filter orientations that fit in the container
        const compatibleOrientations = orientations.filter(o => o.containerCompatibility.fits);

        if (compatibleOrientations.length === 0) {
            // If no orientation fits, find the one with best fit potential
            console.warn('No orientation fits perfectly in container. Finding best compromise...');
            return orientations.reduce((best, current) =>
                current.containerCompatibility.score > best.containerCompatibility.score ? current : best
            );
        }

        // Among compatible orientations, find the one with best cross-section score
        const optimal = compatibleOrientations.reduce((best, current) => {
            // Primary criterion: container utilization
            if (current.containerCompatibility.overallUtilization > best.containerCompatibility.overallUtilization + 0.1) {
                return current;
            }
            // Secondary criterion: cross-section uniformity (if utilization is similar)
            if (Math.abs(current.containerCompatibility.overallUtilization - best.containerCompatibility.overallUtilization) < 0.1) {
                return current.crossSectionScore > best.crossSectionScore ? current : best;
            }
            return best;
        });

        return optimal;
    }

    applyOptimalOrientation(optimalOrientation) {
        console.log('Applying optimal orientation:', optimalOrientation);

        // Apply the optimal rotation
        this.modelRotation = { ...optimalOrientation.rotation };
        this.updateModelTransform();

        // Position the model correctly in the container
        this.positionModelInContainer();

        // Update UI controls
        this.updateControlInputs();

        // Show results to user
        this.showReorientationResults(optimalOrientation);
    }

    positionModelInContainer() {
        if (!this.model) return;

        console.log('Positioning model in container...');

        // First, reset model position to origin to get accurate bounding box
        this.modelPosition = { x: 0, y: 0, z: 0 };
        this.updateModelTransform();

        // Get model's bounding box at origin after rotation
        const modelBox = new THREE.Box3().setFromObject(this.model);
        const modelSize = modelBox.getSize(new THREE.Vector3());
        const modelCenter = modelBox.getCenter(new THREE.Vector3());

        console.log('Model at origin:', {
            center: { x: modelCenter.x, y: modelCenter.y, z: modelCenter.z },
            size: { x: modelSize.x, y: modelSize.y, z: modelSize.z },
            bounds: {
                min: { x: modelBox.min.x, y: modelBox.min.y, z: modelBox.min.z },
                max: { x: modelBox.max.x, y: modelBox.max.y, z: modelBox.max.z }
            }
        });

        // Container parameters
        const containerRadius = this.containerParams.innerDiameter / 2;
        const containerHeight = this.containerParams.height;

        // Calculate optimal position
        // 1. Center horizontally (X and Z) - move model center to origin
        this.modelPosition.x = -modelCenter.x;
        this.modelPosition.z = -modelCenter.z;

        // 2. Position vertically - place model bottom at container bottom with clearance
        const clearance = 5; // 5mm clearance from container bottom
        this.modelPosition.y = clearance - modelBox.min.y;

        // 3. Ensure model fits in container height
        const projectedModelTop = modelBox.max.y + this.modelPosition.y;
        if (projectedModelTop > containerHeight) {
            // If model is too tall, center it vertically in the container
            this.modelPosition.y = (containerHeight - modelSize.y) / 2 - modelBox.min.y;
            console.warn('Model height exceeds container. Centering vertically.');
        }

        // Apply the new position
        this.updateModelTransform();

        // Verify final position
        const finalBox = new THREE.Box3().setFromObject(this.model);
        console.log('Model positioned in container:', {
            position: { ...this.modelPosition },
            finalBounds: {
                min: { x: finalBox.min.x, y: finalBox.min.y, z: finalBox.min.z },
                max: { x: finalBox.max.x, y: finalBox.max.y, z: finalBox.max.z }
            },
            containerBounds: {
                radius: containerRadius,
                height: containerHeight,
                diameter: containerRadius * 2
            },
            fits: {
                radially: Math.max(Math.abs(finalBox.min.x), Math.abs(finalBox.max.x), Math.abs(finalBox.min.z), Math.abs(finalBox.max.z)) <= containerRadius,
                vertically: finalBox.min.y >= 0 && finalBox.max.y <= containerHeight
            }
        });
    }

    showReorientationProgress(message) {
        let progress = document.getElementById('reorientationProgress');
        if (!progress) {
            progress = document.createElement('div');
            progress.id = 'reorientationProgress';
            progress.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 20px 30px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                z-index: 2000;
                text-align: center;
            `;
            document.body.appendChild(progress);
        }

        progress.innerHTML = `
            <i class="fas fa-spinner fa-spin"></i><br>
            ${message}
        `;
        progress.style.display = 'block';
    }

    hideReorientationProgress() {
        const progress = document.getElementById('reorientationProgress');
        if (progress) {
            progress.style.display = 'none';
        }
    }

    showReorientationResults(optimalOrientation) {
        const results = `
            <strong>Auto-Reorientation Complete!</strong><br><br>
            <strong>Container Compatibility:</strong><br>
            • Radial Utilization: ${(optimalOrientation.containerCompatibility.radialUtilization * 100).toFixed(1)}%<br>
            • Vertical Utilization: ${(optimalOrientation.containerCompatibility.verticalUtilization * 100).toFixed(1)}%<br>
            • Overall Utilization: ${(optimalOrientation.containerCompatibility.overallUtilization * 100).toFixed(1)}%<br><br>
            <strong>Cross-Section Quality:</strong><br>
            • Uniformity Score: ${(optimalOrientation.crossSectionScore * 100).toFixed(1)}%<br>
            • Variance: ${optimalOrientation.crossSectionData.variance.toFixed(2)}<br>
            • Range: ${(optimalOrientation.crossSectionData.max - optimalOrientation.crossSectionData.min).toFixed(2)} mm²
        `;

        // Create or update results modal
        let modal = document.getElementById('reorientationResults');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'reorientationResults';
            modal.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border: 2px solid #28a745;
                border-radius: 8px;
                padding: 20px;
                max-width: 400px;
                z-index: 2000;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                font-size: 13px;
                line-height: 1.4;
            `;

            const closeBtn = document.createElement('button');
            closeBtn.textContent = 'Close';
            closeBtn.style.cssText = `
                margin-top: 15px;
                padding: 8px 16px;
                background: #28a745;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                width: 100%;
            `;
            closeBtn.onclick = () => modal.style.display = 'none';

            modal.appendChild(closeBtn);
            document.body.appendChild(modal);
        }

        modal.innerHTML = results + modal.querySelector('button').outerHTML;
        modal.querySelector('button').onclick = () => modal.style.display = 'none';
        modal.style.display = 'block';
    }

    debugModelPosition() {
        if (!this.model) {
            alert('No model loaded');
            return;
        }

        // Get current model information
        const modelBox = new THREE.Box3().setFromObject(this.model);
        const modelSize = modelBox.getSize(new THREE.Vector3());
        const modelCenter = modelBox.getCenter(new THREE.Vector3());

        // Container information
        const containerRadius = this.containerParams.innerDiameter / 2;
        const containerHeight = this.containerParams.height;

        // Check if model fits
        const maxRadialDistance = Math.max(
            Math.abs(modelBox.min.x), Math.abs(modelBox.max.x),
            Math.abs(modelBox.min.z), Math.abs(modelBox.max.z)
        );

        const debugInfo = `
            <h3>Model Debug Information</h3>

            <h4>Current Position:</h4>
            • X: ${this.modelPosition.x.toFixed(2)}mm<br>
            • Y: ${this.modelPosition.y.toFixed(2)}mm<br>
            • Z: ${this.modelPosition.z.toFixed(2)}mm<br>

            <h4>Current Rotation:</h4>
            • X: ${this.modelRotation.x}°<br>
            • Y: ${this.modelRotation.y}°<br>
            • Z: ${this.modelRotation.z}°<br>

            <h4>Model Bounds (World Space):</h4>
            • Min: (${modelBox.min.x.toFixed(2)}, ${modelBox.min.y.toFixed(2)}, ${modelBox.min.z.toFixed(2)})<br>
            • Max: (${modelBox.max.x.toFixed(2)}, ${modelBox.max.y.toFixed(2)}, ${modelBox.max.z.toFixed(2)})<br>
            • Size: (${modelSize.x.toFixed(2)}, ${modelSize.y.toFixed(2)}, ${modelSize.z.toFixed(2)})<br>
            • Center: (${modelCenter.x.toFixed(2)}, ${modelCenter.y.toFixed(2)}, ${modelCenter.z.toFixed(2)})<br>

            <h4>Container Bounds:</h4>
            • Radius: ${containerRadius.toFixed(2)}mm<br>
            • Diameter: ${(containerRadius * 2).toFixed(2)}mm<br>
            • Height: ${containerHeight.toFixed(2)}mm<br>

            <h4>Fit Analysis:</h4>
            • Max Radial Distance: ${maxRadialDistance.toFixed(2)}mm<br>
            • Fits Radially: ${maxRadialDistance <= containerRadius ? '✅ YES' : '❌ NO'}<br>
            • Fits Vertically: ${modelBox.min.y >= 0 && modelBox.max.y <= containerHeight ? '✅ YES' : '❌ NO'}<br>
            • Y Bottom: ${modelBox.min.y.toFixed(2)}mm (should be ≥ 0)<br>
            • Y Top: ${modelBox.max.y.toFixed(2)}mm (should be ≤ ${containerHeight})<br>
        `;

        // Show debug modal
        let modal = document.getElementById('debugModal');
        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'debugModal';
            modal.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border: 2px solid #17a2b8;
                border-radius: 8px;
                padding: 20px;
                max-width: 500px;
                max-height: 80vh;
                overflow-y: auto;
                z-index: 2000;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                font-size: 13px;
                line-height: 1.4;
            `;

            const closeBtn = document.createElement('button');
            closeBtn.textContent = 'Close';
            closeBtn.style.cssText = `
                margin-top: 15px;
                padding: 8px 16px;
                background: #17a2b8;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                width: 100%;
            `;
            closeBtn.onclick = () => modal.style.display = 'none';

            modal.appendChild(closeBtn);
            document.body.appendChild(modal);
        }

        modal.innerHTML = debugInfo + modal.querySelector('button').outerHTML;
        modal.querySelector('button').onclick = () => modal.style.display = 'none';
        modal.style.display = 'block';

        // Also log to console
        console.log('Debug Model Position:', {
            position: this.modelPosition,
            rotation: this.modelRotation,
            bounds: {
                min: { x: modelBox.min.x, y: modelBox.min.y, z: modelBox.min.z },
                max: { x: modelBox.max.x, y: modelBox.max.y, z: modelBox.max.z }
            },
            container: { radius: containerRadius, height: containerHeight },
            fits: {
                radially: maxRadialDistance <= containerRadius,
                vertically: modelBox.min.y >= 0 && modelBox.max.y <= containerHeight
            }
        });
    }

    applyChangesAndRender() {
        // Get current model parameters
        const modelParams = {
            position: { ...this.modelPosition },
            rotation: { ...this.modelRotation },
            container: { ...this.containerParams }
        };

        // Add to form data for processing
        const form = document.getElementById('uploadForm');

        // Remove existing model params
        const existingParams = form.querySelectorAll('input[name^="model_"]');
        existingParams.forEach(input => input.remove());

        // Add new model parameters as hidden inputs
        Object.entries(modelParams.position).forEach(([axis, value]) => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = `model_position_${axis}`;
            input.value = value;
            form.appendChild(input);
        });

        Object.entries(modelParams.rotation).forEach(([axis, value]) => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = `model_rotation_${axis}`;
            input.value = value;
            form.appendChild(input);
        });

        Object.entries(modelParams.container).forEach(([param, value]) => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = `container_${param}`;
            input.value = value;
            form.appendChild(input);
        });

        console.log('Model parameters applied:', modelParams);
        alert('Model parameters have been applied. You can now submit for processing.');
    }

    animate() {
        requestAnimationFrame(() => this.animate());

        this.controls.update();
        this.renderer.render(this.scene, this.camera);
    }

    render() {
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }

    onWindowResize() {
        this.camera.aspect = this.container.clientWidth / this.container.clientHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
        this.render(); // Re-render after resize
    }
}

// Initialize 3D viewer
let viewer3D = null;
// 通用alert函数
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 插入到页面顶部
    const mainContent = document.querySelector('main');
    const firstChild = mainContent.firstElementChild;
    mainContent.insertBefore(alertDiv, firstChild);

    // 自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Check system status and show banner if needed
function checkSystemStatus() {
    fetch('/api/system/status')
        .then(response => response.json())
        .then(data => {
            const statusBanner = document.getElementById('systemStatus');
            const statusMessage = document.getElementById('statusMessage');

            if (data.mode === 'simulation') {
                statusBanner.className = 'system-status simulation';
                statusMessage.innerHTML = `
                    <strong>Simulation Mode Active</strong> -
                    TomoEngine core is not available. Submissions will generate test videos instead of actual renders.
                `;
                statusBanner.style.display = 'block';
            } else {
                statusBanner.className = 'system-status';
                statusMessage.innerHTML = `
                    <strong>Production Mode</strong> -
                    TomoEngine is available and ready for processing.
                `;
                statusBanner.style.display = 'block';

                // Hide production status after 3 seconds
                setTimeout(() => {
                    statusBanner.style.display = 'none';
                }, 3000);
            }
        })
        .catch(error => {
            console.error('Failed to check system status:', error);
        });
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize 3D viewer
    viewer3D = new Model3DViewer('threejs-container');
    window.modelViewer = viewer3D;  // 确保全局访问

    // Check system status
    checkSystemStatus();

    // Initialize container settings collapse functionality
    const containerSettingsPanel = document.getElementById('viewer-container-settings');
    if (containerSettingsPanel) {
        const containerTitle = containerSettingsPanel.querySelector('h4');

        if (containerTitle) {
            containerTitle.addEventListener('click', function() {
                containerSettingsPanel.classList.toggle('collapsed');
            });
        }
    }

    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const submitBtn = document.getElementById('submitBtn');
    const uploadForm = document.getElementById('uploadForm');
    const showAdvanced = document.getElementById('showAdvanced');
    const advancedOptions = document.getElementById('advancedOptions');
    const progressContainer = document.getElementById('progressContainer');
    const progressText = document.getElementById('progressText');

    // 检查是否从状态页面返回
    function checkReturnFromStatus() {
        const referrer = document.referrer;
        const currentUrl = window.location.href;

        // 如果从状态页面返回，显示提示信息
        if (referrer && referrer.includes('/status/')) {
            setTimeout(() => {
                showAlert('Welcome back! To start a new processing job, please select your file below.', 'info');
            }, 500); // 延迟显示，确保页面完全加载
        }

        // 确保页面状态正确初始化
        resetPageState();
    }

    // 重置页面状态
    function resetPageState() {
        // 确保文件输入框为空
        fileInput.value = '';

        // 隐藏文件信息
        fileInfo.style.display = 'none';

        // 禁用提交按钮
        submitBtn.disabled = true;
        submitBtn.textContent = 'Upload & Process';

        // 隐藏进度条
        progressContainer.style.display = 'none';

        // 重置上传区域样式
        uploadArea.classList.remove('dragover');
    }

    // 页面加载时执行检查
    checkReturnFromStatus();

    // 监听页面可见性变化，确保状态正确
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            // 页面变为可见时，检查文件输入状态
            if (!fileInput.files || fileInput.files.length === 0) {
                // 如果没有文件，确保按钮是禁用的
                if (!submitBtn.disabled) {
                    resetPageState();
                }
            }
        }
    });

    // File upload handling
    uploadArea.addEventListener('click', (e) => {
        console.log('Upload area clicked', e);

        // 清除可能存在的错误提示
        const existingAlerts = document.querySelectorAll('.alert-danger');
        existingAlerts.forEach(alert => {
            if (alert.textContent.includes('Please select') || alert.textContent.includes('File size')) {
                alert.remove();
            }
        });

        try {
            fileInput.click();
            console.log('File input click triggered');
        } catch (error) {
            console.error('Error triggering file input click:', error);
        }
    });
    
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        // 清除可能存在的错误提示
        const existingAlerts = document.querySelectorAll('.alert-danger');
        existingAlerts.forEach(alert => {
            if (alert.textContent.includes('Please select') || alert.textContent.includes('File size')) {
                alert.remove();
            }
        });

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });
    
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        } else {
            // 如果用户取消了文件选择，重置状态
            resetPageState();
        }
    });

    function handleFileSelect(file) {
        // Check file type
        const allowedTypes = ['.stl', '.obj'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

        if (!allowedTypes.includes(fileExtension)) {
            showAlert('Please select a valid STL or OBJ file.', 'error');
            resetPageState(); // 重置状态
            return;
        }

        // Check file size (50MB limit)
        if (file.size > 50 * 1024 * 1024) {
            showAlert('File size must be less than 50MB.', 'error');
            resetPageState(); // 重置状态
            return;
        }

        // Update UI
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        fileInfo.style.display = 'block';
        submitBtn.disabled = false;
        submitBtn.textContent = 'Upload & Process'; // 确保按钮文本正确

        // 隐藏进度条（如果之前显示过）
        progressContainer.style.display = 'none';

        // Store file reference
        fileInput.files = createFileList(file);

        // Load file into 3D viewer if it's an STL or OBJ file
        if (fileExtension === '.stl' && viewer3D) {
            try {
                viewer3D.loadSTLModel(file);
                console.log('Loading STL file into 3D viewer:', file.name);
            } catch (error) {
                console.error('Error loading STL into 3D viewer:', error);
            }
        } else if (fileExtension === '.obj' && viewer3D) {
            try {
                viewer3D.loadOBJModel(file);
                console.log('Loading OBJ file into 3D viewer:', file.name);
            } catch (error) {
                console.error('Error loading OBJ into 3D viewer:', error);

                // 显示友好的错误提示，但不阻止文件上传
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-warning';
                errorDiv.style.marginTop = '10px';
                errorDiv.innerHTML = `
                    <strong>3D Preview Warning:</strong> Could not display OBJ file in 3D viewer.
                    The file will still be processed normally.
                    <small>Error: ${error.message}</small>
                `;

                // 在文件信息下方显示警告
                const fileInfoDiv = document.getElementById('fileInfo');
                if (fileInfoDiv) {
                    // 移除之前的警告
                    const existingWarning = fileInfoDiv.querySelector('.alert-warning');
                    if (existingWarning) {
                        existingWarning.remove();
                    }
                    fileInfoDiv.appendChild(errorDiv);
                }
            }
        }
    }

    function createFileList(file) {
        const dt = new DataTransfer();
        dt.items.add(file);
        return dt.files;
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Advanced options toggle - 只对有权限的用户启用
    if (showAdvanced) {
        showAdvanced.addEventListener('change', function() {
            if (this.checked) {
                advancedOptions.classList.add('show');
            } else {
                advancedOptions.classList.remove('show');
            }
        });
    }

    // Models Option toggle and logic
    const showModels = document.getElementById('showModels');
    const modelsOptions = document.getElementById('modelsOptions');
    const capsuleModel = document.getElementById('capsuleModel');
    const customModel = document.getElementById('customModel');
    const capsuleOptions = document.getElementById('capsuleOptions');
    const customOptions = document.getElementById('customOptions');
    const capsuleByModel = document.getElementById('capsuleByModel');
    const capsuleBySerial = document.getElementById('capsuleBySerial');
    const modelDropdown = document.getElementById('modelDropdown');
    const serialInput = document.getElementById('serialInput');

    if (showModels) {
        // Models Option toggle
        showModels.addEventListener('change', function() {
            if (this.checked) {
                modelsOptions.classList.add('show');
                modelsOptions.style.display = 'block';
            } else {
                modelsOptions.classList.remove('show');
                modelsOptions.style.display = 'none';
                // Reset all selections when hiding
                resetModelsOptions();
            }
        });

        // Primary model type selection
        capsuleModel.addEventListener('change', function() {
            if (this.checked) {
                capsuleOptions.style.display = 'block';
                customOptions.style.display = 'none';
                resetCapsuleOptions();
            }
        });

        customModel.addEventListener('change', function() {
            if (this.checked) {
                customOptions.style.display = 'block';
                capsuleOptions.style.display = 'none';
            }
        });

        // Capsule selection type
        capsuleByModel.addEventListener('change', function() {
            if (this.checked) {
                modelDropdown.style.display = 'block';
                serialInput.style.display = 'none';
                document.getElementById('serialNumber').value = '';
            }
        });

        capsuleBySerial.addEventListener('change', function() {
            if (this.checked) {
                serialInput.style.display = 'block';
                modelDropdown.style.display = 'none';
                document.getElementById('modelSelect').value = '';
            }
        });

        // Helper functions
        function resetModelsOptions() {
            // Reset primary selection
            capsuleModel.checked = false;
            customModel.checked = false;

            // Hide sub-options
            capsuleOptions.style.display = 'none';
            customOptions.style.display = 'none';

            resetCapsuleOptions();
        }

        function resetCapsuleOptions() {
            // Reset capsule type selection
            capsuleByModel.checked = false;
            capsuleBySerial.checked = false;

            // Hide and reset inputs
            modelDropdown.style.display = 'none';
            serialInput.style.display = 'none';
            document.getElementById('modelSelect').value = '';
            document.getElementById('serialNumber').value = '';
        }
    }

    // Container Settings toggle and logic
    const showContainer = document.getElementById('showContainer');
    const containerSettings = document.getElementById('containerSettings');
    const containerRadius = document.getElementById('containerRadius');
    const containerHeight = document.getElementById('containerHeight');

    if (showContainer) {
        // Container Settings toggle
        showContainer.addEventListener('change', function() {
            if (this.checked) {
                containerSettings.classList.add('show');
                containerSettings.style.display = 'block';
                updateContainerPreview();
            } else {
                containerSettings.classList.remove('show');
                containerSettings.style.display = 'none';
            }
        });

        // Container parameter change listeners
        if (containerRadius) {
            containerRadius.addEventListener('input', updateContainerPreview);
        }

        if (containerHeight) {
            containerHeight.addEventListener('input', updateContainerPreview);
        }
    }

    // Container helper functions
    function updateContainerPreview() {
        const radius = parseFloat(containerRadius?.value || 50);
        const height = parseFloat(containerHeight?.value || 100);

        // Calculate container properties
        const diameter = radius * 2;
        const volume = Math.PI * radius * radius * height / 1000; // Convert to cm³
        const surfaceArea = 2 * Math.PI * radius * (radius + height) / 100; // Convert to cm²

        // Update display
        const volumeElement = document.getElementById('containerVolume');
        const diameterElement = document.getElementById('containerDiameter');
        const surfaceAreaElement = document.getElementById('containerSurfaceArea');

        if (volumeElement) volumeElement.textContent = volume.toFixed(1) + ' cm³';
        if (diameterElement) diameterElement.textContent = diameter.toFixed(1) + ' mm';
        if (surfaceAreaElement) surfaceAreaElement.textContent = surfaceArea.toFixed(1) + ' cm²';
    }

    function resetContainer() {
        if (containerRadius) containerRadius.value = 50.0;
        if (containerHeight) containerHeight.value = 100.0;
        updateContainerPreview();
    }

    function previewContainer() {
        const radius = parseFloat(containerRadius?.value || 50);
        const height = parseFloat(containerHeight?.value || 100);

        alert(`Container Preview:\n\nRadius: ${radius} mm\nHeight: ${height} mm\nDiameter: ${radius * 2} mm\nVolume: ${(Math.PI * radius * radius * height / 1000).toFixed(1)} cm³`);
    }

    // Range input updates
    const resolutionInput = document.getElementById('resolution');
    const resolutionValue = document.getElementById('resolutionValue');
    if (resolutionInput && resolutionValue) {
        resolutionInput.addEventListener('input', function() {
            resolutionValue.textContent = this.value;
        });
    }

    const brightnessInput = document.getElementById('brightness');
    const brightnessValue = document.getElementById('brightnessValue');
    if (brightnessInput && brightnessValue) {
        brightnessInput.addEventListener('input', function() {
            brightnessValue.textContent = this.value;
        });
    }

    // Form submission
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Create form data first
        const formData = new FormData(this);

        // Check for model collision before submission
        const hasCollision = document.getElementById('collisionWarning').style.display !== 'none';

        if (hasCollision) {
            // Show custom confirmation dialog for collision
            showCollisionConfirmDialog(function(confirmed) {
                if (confirmed) {
                    proceedWithSubmission(formData);
                }
            });
            return;
        }

        // No collision, proceed directly
        proceedWithSubmission(formData);
    });

    // Function to handle the actual submission process
    function proceedWithSubmission(formData) {
        console.log('proceedWithSubmission called');

        // Add model position and rotation parameters to form data
        // 直接从输入字段获取当前值，确保数据同步
        const modelPosX = parseFloat(document.getElementById('modelPosX')?.value || 0);
        const modelPosY = parseFloat(document.getElementById('modelPosY')?.value || 0);
        const modelPosZ = parseFloat(document.getElementById('modelPosZ')?.value || 0);
        const modelRotX = parseFloat(document.getElementById('modelRotX')?.value || 0);
        const modelRotY = parseFloat(document.getElementById('modelRotY')?.value || 0);
        const modelRotZ = parseFloat(document.getElementById('modelRotZ')?.value || 0);

        formData.append('model_pos_x', modelPosX);
        formData.append('model_pos_y', modelPosY);
        formData.append('model_pos_z', modelPosZ);
        formData.append('model_rot_x', modelRotX);
        formData.append('model_rot_y', modelRotY);
        formData.append('model_rot_z', modelRotZ);

        // Add model scale parameter
        const modelScale = parseFloat(document.getElementById('modelScale')?.value || 100);
        formData.append('model_scale', modelScale);

        // Add container parameters
        const containerOuterDiameter = document.getElementById('containerOuterDiameter')?.value;
        const containerInnerDiameter = document.getElementById('containerInnerDiameter')?.value;

        if (containerOuterDiameter) {
            formData.append('container_outer_diameter', containerOuterDiameter);
        }
        if (containerInnerDiameter) {
            formData.append('container_inner_diameter', containerInnerDiameter);
        }

        console.log('Added model transform parameters:', {
            position: { x: modelPosX, y: modelPosY, z: modelPosZ },
            rotation: { x: modelRotX, y: modelRotY, z: modelRotZ },
            scale: modelScale + '%'
        });

        console.log('Added container parameters:', {
            outerDiameter: containerOuterDiameter || 'default (30mm)',
            innerDiameter: containerInnerDiameter || 'default (27mm)'
        });

        // Show progress
        progressContainer.style.display = 'block';
        submitBtn.disabled = true;
        submitBtn.textContent = 'Processing...';

        // Upload file
        fetch('/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('File uploaded successfully! Check your email for confirmation.', 'success');
                
                // Redirect to status page
                setTimeout(() => {
                    window.location.href = data.status_url;
                }, 2000);
            } else {
                throw new Error(data.error || 'Upload failed');
            }
        })
        .catch(error => {
            showAlert('Error: ' + error.message, 'error');

            // 重置按钮状态，但保持文件选择状态
            submitBtn.disabled = false;
            submitBtn.textContent = 'Upload & Process';
            progressContainer.style.display = 'none';

            // 保持文件信息显示，让用户可以重新提交
            // 不调用 resetPageState() 以保持文件选择状态
        });
    }

    // Email validation
    const emailInput = document.getElementById('email');
    if (emailInput) {
        emailInput.addEventListener('input', function() {
            const email = this.value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
            if (email && !emailRegex.test(email)) {
                this.style.borderColor = '#e74c3c';
            } else {
                this.style.borderColor = '#ecf0f1';
            }
        });
    }

    // Custom collision confirmation dialog
    function showCollisionConfirmDialog(callback) {
        console.log('showCollisionConfirmDialog called');
        const collisionDetails = document.getElementById('collisionDetails').innerHTML;

        // Create modal overlay
        const overlay = document.createElement('div');
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 3000;
            display: flex;
            justify-content: center;
            align-items: center;
        `;

        // Create modal content
        const modal = document.createElement('div');
        modal.style.cssText = `
            background: white;
            border-radius: 8px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            text-align: center;
        `;

        modal.innerHTML = `
            <div style="color: #e74c3c; font-size: 24px; margin-bottom: 15px;">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 style="color: #e74c3c; margin-bottom: 15px;">Model Exceeds Container Bounds</h3>
            <div style="text-align: left; margin-bottom: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <strong>Issues detected:</strong><br>
                ${collisionDetails}
            </div>
            <p style="margin-bottom: 25px; color: #666;">
                This may affect rendering quality or cause processing issues.<br>
                <strong>Do you want to continue with the submission anyway?</strong>
            </p>
            <div style="display: flex; gap: 15px; justify-content: center;">
                <button id="cancelSubmit" style="
                    padding: 10px 20px;
                    background: #95a5a6;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 14px;
                ">Cancel</button>
                <button id="confirmSubmit" style="
                    padding: 10px 20px;
                    background: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 14px;
                ">Continue Anyway</button>
            </div>
        `;

        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // Handle button clicks
        document.getElementById('cancelSubmit').onclick = function() {
            console.log('User cancelled submission');
            document.body.removeChild(overlay);
            callback(false);
        };

        document.getElementById('confirmSubmit').onclick = function() {
            console.log('User confirmed submission despite collision');
            document.body.removeChild(overlay);
            callback(true);
        };

        // Close on overlay click
        overlay.onclick = function(e) {
            if (e.target === overlay) {
                document.body.removeChild(overlay);
                callback(false);
            }
        };
    }
});
</script>
{% endblock %}
